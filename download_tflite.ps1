# TensorFlow Lite 下载脚本
# 下载预编译的TensorFlow Lite C++库

Write-Host "正在下载TensorFlow Lite C++库..." -ForegroundColor Green

# 创建third_party目录
$thirdPartyDir = "third_party"
$tfliteDir = "$thirdPartyDir/tensorflow-lite"

if (!(Test-Path $thirdPartyDir)) {
    New-Item -ItemType Directory -Path $thirdPartyDir
}

if (!(Test-Path $tfliteDir)) {
    New-Item -ItemType Directory -Path $tfliteDir
}

# TensorFlow Lite预编译库下载链接
$tfliteUrl = "https://github.com/tensorflow/tensorflow/releases/download/v2.13.0/libtensorflowlite_c-2.13.0-win-x64.zip"
$zipFile = "$tfliteDir/tflite.zip"

try {
    Write-Host "下载TensorFlow Lite库..." -ForegroundColor Yellow
    Invoke-WebRequest -Uri $tfliteUrl -OutFile $zipFile -UseBasicParsing
    
    Write-Host "解压文件..." -ForegroundColor Yellow
    Expand-Archive -Path $zipFile -DestinationPath $tfliteDir -Force
    
    # 删除zip文件
    Remove-Item $zipFile
    
    Write-Host "TensorFlow Lite库下载完成！" -ForegroundColor Green
    Write-Host "库文件位置: $tfliteDir" -ForegroundColor Cyan
    
    # 显示目录结构
    Write-Host "`n目录结构:" -ForegroundColor Cyan
    Get-ChildItem -Path $tfliteDir -Recurse | Select-Object Name, FullName | Format-Table -AutoSize
    
} catch {
    Write-Host "下载失败，尝试备用方案..." -ForegroundColor Red
    
    # 备用方案：创建基本的头文件结构
    Write-Host "创建基本的TensorFlow Lite头文件结构..." -ForegroundColor Yellow
    
    $includeDir = "$tfliteDir/include"
    $libDir = "$tfliteDir/lib"
    
    New-Item -ItemType Directory -Path $includeDir -Force
    New-Item -ItemType Directory -Path $libDir -Force
    
    # 创建基本的头文件
    $basicHeader = @"
#pragma once
// 基本的TensorFlow Lite头文件占位符
// 用于编译时的类型定义

namespace tflite {
    class Interpreter {};
    class FlatBufferModel {};
}
"@
    
    $basicHeader | Out-File -FilePath "$includeDir/tensorflow_lite.h" -Encoding UTF8
    
    Write-Host "已创建基本头文件结构用于编译" -ForegroundColor Green
}

Write-Host "`n接下来的步骤:" -ForegroundColor Cyan
Write-Host "1. 如果下载成功，TensorFlow Lite库已准备就绪" -ForegroundColor White
Write-Host "2. 如果下载失败，将使用模拟模式进行演示" -ForegroundColor White
Write-Host "3. 运行 cmake 重新配置项目以包含TensorFlow Lite" -ForegroundColor White
