#pragma once

#include <QWidget>
#include <QPainter>
#include <QTimer>
#include <QFont>
#include <QFontMetrics>
#include <QRectF>
#include <QString>

// 简化的检测结果结构
struct DetectionResult {
    QString className;      // 类别名称
    float confidence;       // 置信度 (0.0-1.0)
    QRectF boundingBox;     // 边界框 (相对坐标 0.0-1.0)
    int classId;           // 类别ID

    DetectionResult() : confidence(0.0f), classId(-1) {}
    DetectionResult(const QString& name, float conf, const QRectF& box, int id = -1)
        : className(name), confidence(conf), boundingBox(box), classId(id) {}
};

/**
 * @brief 检测结果叠加显示组件
 * 
 * 在视频画面上叠加显示AI检测结果
 */
class DetectionOverlay : public QWidget {
    Q_OBJECT

public:
    explicit DetectionOverlay(QWidget* parent = nullptr);

    /**
     * @brief 更新检测结果
     */
    void updateDetections(const std::vector<DetectionResult>& results);

    /**
     * @brief 清除检测结果
     */
    void clearDetections();

    /**
     * @brief 设置是否显示置信度
     */
    void setShowConfidence(bool show) { showConfidence_ = show; update(); }

    /**
     * @brief 设置是否显示边界框
     */
    void setShowBoundingBoxes(bool show) { showBoundingBoxes_ = show; update(); }

protected:
    void paintEvent(QPaintEvent* event) override;

private:
    std::vector<DetectionResult> detections_;
    bool showConfidence_;
    bool showBoundingBoxes_;
    QFont labelFont_;
    
    // 颜色配置
    QColor getBoundingBoxColor(int classId) const;
    QColor getLabelBackgroundColor(int classId) const;
};
