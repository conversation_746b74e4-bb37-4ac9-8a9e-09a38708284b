# 简化的YOLO模型下载脚本
# 下载YOLO模型到D盘

param(
    [string]$ModelsDir = "D:\AI_Models",
    [switch]$Force = $false
)

Write-Host "=== YOLO模型下载脚本 ===" -ForegroundColor Green
Write-Host "目标目录: $ModelsDir" -ForegroundColor Yellow

# 创建目录结构
$yoloDir = Join-Path $ModelsDir "yolo"
$yolov5Dir = Join-Path $yoloDir "yolov5"
$yolov8Dir = Join-Path $yoloDir "yolov8"

Write-Host "创建目录结构..." -ForegroundColor Yellow
New-Item -ItemType Directory -Path $ModelsDir -Force | Out-Null
New-Item -ItemType Directory -Path $yoloDir -Force | Out-Null
New-Item -ItemType Directory -Path $yolov5Dir -Force | Out-Null
New-Item -ItemType Directory -Path $yolov8Dir -Force | Out-Null

# 下载函数
function Download-YOLOModel {
    param($name, $url, $filePath)
    
    if ((Test-Path $filePath) -and -not $Force) {
        Write-Host "✓ $name 已存在: $filePath" -ForegroundColor Green
        return $true
    }
    
    Write-Host "下载 $name..." -ForegroundColor Yellow
    Write-Host "  URL: $url" -ForegroundColor Gray
    Write-Host "  保存到: $filePath" -ForegroundColor Gray
    
    try {
        $ProgressPreference = 'SilentlyContinue'
        Invoke-WebRequest -Uri $url -OutFile $filePath -UseBasicParsing
        
        if (Test-Path $filePath) {
            $fileSize = (Get-Item $filePath).Length / 1MB
            Write-Host "✓ $name 下载完成 (${fileSize:F1} MB)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ $name 下载失败" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ $name 下载出错: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 开始下载模型
Write-Host "`n开始下载YOLO模型..." -ForegroundColor Green

$successCount = 0

# YOLOv5s
$yolov5sPath = Join-Path $yolov5Dir "yolov5s.onnx"
$yolov5sUrl = "https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5s.onnx"
if (Download-YOLOModel -name "YOLOv5s" -url $yolov5sUrl -filePath $yolov5sPath) {
    $successCount++
}

# YOLOv5m
$yolov5mPath = Join-Path $yolov5Dir "yolov5m.onnx"
$yolov5mUrl = "https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5m.onnx"
if (Download-YOLOModel -name "YOLOv5m" -url $yolov5mUrl -filePath $yolov5mPath) {
    $successCount++
}

# YOLOv8s
$yolov8sPath = Join-Path $yolov8Dir "yolov8s.onnx"
$yolov8sUrl = "https://github.com/ultralytics/ultralytics/releases/download/v8.0.0/yolov8s.onnx"
if (Download-YOLOModel -name "YOLOv8s" -url $yolov8sUrl -filePath $yolov8sPath) {
    $successCount++
}

# YOLOv8m
$yolov8mPath = Join-Path $yolov8Dir "yolov8m.onnx"
$yolov8mUrl = "https://github.com/ultralytics/ultralytics/releases/download/v8.0.0/yolov8m.onnx"
if (Download-YOLOModel -name "YOLOv8m" -url $yolov8mUrl -filePath $yolov8mPath) {
    $successCount++
}

# 复制COCO标签文件到D盘
$sourceLabels = "models\coco_labels.txt"
$targetLabels = Join-Path $ModelsDir "coco_labels.txt"
if (Test-Path $sourceLabels) {
    Copy-Item $sourceLabels $targetLabels -Force
    Write-Host "✓ COCO标签文件已复制到: $targetLabels" -ForegroundColor Green
}

# 创建配置文件
$configPath = Join-Path $yoloDir "model_config.json"
$configContent = @"
{
    "default_model": "yolov5s",
    "models": {
        "yolov5s": {
            "path": "yolo/yolov5/yolov5s.onnx",
            "input_size": 640,
            "confidence_threshold": 0.25,
            "nms_threshold": 0.45,
            "description": "YOLOv5s - 快速检测模型"
        },
        "yolov5m": {
            "path": "yolo/yolov5/yolov5m.onnx",
            "input_size": 640,
            "confidence_threshold": 0.25,
            "nms_threshold": 0.45,
            "description": "YOLOv5m - 平衡性能模型"
        },
        "yolov8s": {
            "path": "yolo/yolov8/yolov8s.onnx",
            "input_size": 640,
            "confidence_threshold": 0.25,
            "nms_threshold": 0.45,
            "description": "YOLOv8s - 最新快速模型"
        },
        "yolov8m": {
            "path": "yolo/yolov8/yolov8m.onnx",
            "input_size": 640,
            "confidence_threshold": 0.25,
            "nms_threshold": 0.45,
            "description": "YOLOv8m - 最新平衡模型"
        }
    }
}
"@

$configContent | Out-File -FilePath $configPath -Encoding UTF8

Write-Host "`n=== 下载完成 ===" -ForegroundColor Green
Write-Host "成功下载: $successCount/4 个模型" -ForegroundColor Yellow
Write-Host "模型目录: $ModelsDir" -ForegroundColor Yellow
Write-Host "配置文件: $configPath" -ForegroundColor Yellow

if ($successCount -gt 0) {
    Write-Host "`n✓ YOLO模型准备完成！" -ForegroundColor Green
    Write-Host "下一步：集成到OpenCVVisionEngine中" -ForegroundColor Cyan
} else {
    Write-Host "`n✗ 没有成功下载任何模型，请检查网络连接。" -ForegroundColor Red
}
