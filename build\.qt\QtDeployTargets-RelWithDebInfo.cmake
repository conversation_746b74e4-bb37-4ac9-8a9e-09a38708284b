set(__QT_DEPLOY_TARGET_AIVisionPlatform_FILE D:/QT  pros/111/build/bin/RelWithDebInfo/AIVisionPlatform.exe)
set(__QT_DEPLOY_TARGET_AIVisionPlatform_TYPE EXECUTABLE)
set(__QT_DEPLOY_TARGET_AIVisionPlatform_RUNTIME_DLLS D:/QT/6.9.1/msvc2022_64/bin/Qt6MultimediaWidgets.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110.dll;D:/QT/6.9.1/msvc2022_64/bin/Qt6Widgets.dll;D:/QT/6.9.1/msvc2022_64/bin/Qt6Multimedia.dll;D:/QT/6.9.1/msvc2022_64/bin/Qt6Network.dll;D:/QT/6.9.1/msvc2022_64/bin/Qt6Gui.dll;D:/QT/6.9.1/msvc2022_64/bin/Qt6Core.dll)
