QMAKE_CXX.QT_COMPILER_STDCXX = 199711L
QMAKE_CXX.QMAKE_MSC_VER = 1941
QMAKE_CXX.QMAKE_MSC_FULL_VER = 194134120
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_MSC_VER \
    QMAKE_MSC_FULL_VER
QMAKE_CXX.INCDIRS = \
    D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\include \
    D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\include \
    D:\\VS2\\VC\\Auxiliary\\VS\\include \
    "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt" \
    "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\um" \
    "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\shared" \
    "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\winrt" \
    "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\cppwinrt" \
    "C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um"
QMAKE_CXX.LIBDIRS = \
    D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\lib\\x64 \
    D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\lib\\x64 \
    "C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\lib\\um\\x64" \
    "C:\\Program Files (x86)\\Windows Kits\\10\\lib\\10.0.22621.0\\ucrt\\x64" \
    "C:\\Program Files (x86)\\Windows Kits\\10\\\\lib\\10.0.22621.0\\\\um\\x64"
