#include "../../include/DetectionOverlay.h"
#include <QPaintEvent>
#include <QApplication>

DetectionOverlay::DetectionOverlay(QWidget* parent)
    : QWidget(parent)
    , showConfidence_(true)
    , showBoundingBoxes_(true)
{
    setAttribute(Qt::WA_TransparentForMouseEvents);
    setAttribute(Qt::WA_TranslucentBackground);

    // 确保覆盖层可见
    setVisible(true);
    show();
    
    // 设置字体
    labelFont_ = QFont("Arial", 12, QFont::Bold);
}

void DetectionOverlay::updateDetections(const std::vector<DetectionResult>& results) {
    detections_ = results;
    update(); // 触发重绘
}

void DetectionOverlay::clearDetections() {
    detections_.clear();
    update();
}

void DetectionOverlay::paintEvent(QPaintEvent* event) {
    Q_UNUSED(event)

    qDebug() << "DetectionOverlay::paintEvent called, detections count:" << detections_.size()
             << "Widget size:" << size();

    if (detections_.empty()) {
        return;
    }

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    const QSize widgetSize = size();
    
    for (const auto& detection : detections_) {
        // 将相对坐标转换为绝对坐标
        QRectF relativeBox = detection.boundingBox;
        QRectF absoluteBox(
            relativeBox.x() * widgetSize.width(),
            relativeBox.y() * widgetSize.height(),
            relativeBox.width() * widgetSize.width(),
            relativeBox.height() * widgetSize.height()
        );

        qDebug() << "Drawing detection:" << detection.className
                 << "Relative box:" << relativeBox
                 << "Absolute box:" << absoluteBox
                 << "Widget size:" << widgetSize;
        
        // 绘制边界框
        if (showBoundingBoxes_) {
            // 绘制外边框 - 使用鲜艳的红色，非常粗的线条
            QPen outerPen(QColor(255, 0, 0, 255), 8); // 红色，8像素粗
            outerPen.setStyle(Qt::SolidLine);
            painter.setPen(outerPen);
            painter.setBrush(Qt::NoBrush);
            painter.drawRect(absoluteBox);

            qDebug() << "Drawing red border at:" << absoluteBox;

            // 绘制内边框 - 白色
            QPen innerPen(QColor(255, 255, 255, 255), 4); // 白色，4像素粗
            painter.setPen(innerPen);
            painter.drawRect(absoluteBox.adjusted(4, 4, -4, -4));

            // 绘制最内层边框 - 绿色
            QPen innerestPen(QColor(0, 255, 0, 255), 2); // 绿色，2像素粗
            painter.setPen(innerestPen);
            painter.drawRect(absoluteBox.adjusted(6, 6, -6, -6));
        }
        
        // 绘制标签
        QString labelText = detection.className;
        if (showConfidence_) {
            labelText += QString(" (%1%)").arg(QString::number(detection.confidence * 100, 'f', 1));
        }
        
        painter.setFont(labelFont_);
        QFontMetrics fm(labelFont_);
        QRect textRect = fm.boundingRect(labelText);
        
        // 标签背景位置
        QRectF labelBgRect(
            absoluteBox.x(),
            absoluteBox.y() - textRect.height() - 8,
            textRect.width() + 16,
            textRect.height() + 8
        );
        
        // 确保标签在窗口内
        if (labelBgRect.y() < 0) {
            labelBgRect.moveTop(absoluteBox.bottom());
        }
        
        // 绘制标签背景 - 使用鲜艳的红色
        painter.setBrush(QColor(255, 0, 0, 220)); // 鲜艳红色背景
        painter.setPen(QPen(QColor(255, 255, 255), 3)); // 白色粗边框
        painter.drawRect(labelBgRect);

        qDebug() << "Drawing label background at:" << labelBgRect;

        // 绘制标签文字 - 添加阴影效果
        painter.setPen(QColor(0, 0, 0, 150)); // 黑色阴影
        painter.drawText(
            labelBgRect.x() + 9,
            labelBgRect.y() + fm.ascent() + 5,
            labelText
        );

        painter.setPen(Qt::white); // 白色文字
        painter.drawText(
            labelBgRect.x() + 8,
            labelBgRect.y() + fm.ascent() + 4,
            labelText
        );
    }
}

QColor DetectionOverlay::getBoundingBoxColor(int classId) const {
    // 为不同类别使用不同颜色
    static const QColor colors[] = {
        QColor(255, 0, 0),     // 红色
        QColor(0, 255, 0),     // 绿色
        QColor(0, 0, 255),     // 蓝色
        QColor(255, 255, 0),   // 黄色
        QColor(255, 0, 255),   // 洋红
        QColor(0, 255, 255),   // 青色
        QColor(255, 128, 0),   // 橙色
        QColor(128, 0, 255),   // 紫色
        QColor(255, 192, 203), // 粉色
        QColor(0, 128, 0),     // 深绿色
    };
    
    const int numColors = sizeof(colors) / sizeof(colors[0]);
    return colors[classId % numColors];
}

QColor DetectionOverlay::getLabelBackgroundColor(int classId) const {
    QColor boxColor = getBoundingBoxColor(classId);
    boxColor.setAlpha(200); // 半透明
    return boxColor;
}
