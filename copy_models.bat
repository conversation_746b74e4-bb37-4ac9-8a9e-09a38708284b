@echo off
echo Copying models to build directories...

REM Debug版本
if exist "build\debug" (
    if not exist "build\debug\models" mkdir "build\debug\models"
    xcopy "models\*" "build\debug\models\" /E /I /Y
    echo Models copied to debug directory
)

REM Release版本
if exist "build\release" (
    if not exist "build\release\models" mkdir "build\release\models"
    xcopy "models\*" "build\release\models\" /E /I /Y
    echo Models copied to release directory
)

REM Qt Creator构建目录
for /d %%i in (build\Desktop_Qt_*) do (
    if not exist "%%i\models" mkdir "%%i\models"
    xcopy "models\*" "%%i\models\" /E /I /Y
    echo Models copied to %%i
)

echo Model copying completed!
pause
