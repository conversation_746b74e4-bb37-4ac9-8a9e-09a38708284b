QT += core widgets multimedia multimediawidgets network

CONFIG += c++17

TARGET = AIVersion
TEMPLATE = app

# 确保Qt路径正确
QT_DIR = D:/QT/6.9.1/msvc2022_64

# 定义应用程序信息
VERSION = 1.0.0
QMAKE_TARGET_COMPANY = "AI Vision Platform"
QMAKE_TARGET_PRODUCT = "AI Vision Platform"
QMAKE_TARGET_DESCRIPTION = "AI图像识别平台"
QMAKE_TARGET_COPYRIGHT = "Copyright 2024"

# 输出目录配置
CONFIG(debug, debug|release) {
    DESTDIR = $$PWD/build/debug
    OBJECTS_DIR = $$PWD/build/debug/obj
    MOC_DIR = $$PWD/build/debug/moc
    RCC_DIR = $$PWD/build/debug/rcc
    UI_DIR = $$PWD/build/debug/ui
}

CONFIG(release, debug|release) {
    DESTDIR = $$PWD/build/release
    OBJECTS_DIR = $$PWD/build/release/obj
    MOC_DIR = $$PWD/build/release/moc
    RCC_DIR = $$PWD/build/release/rcc
    UI_DIR = $$PWD/build/release/ui
}

# OpenCV配置
OPENCV_DIR = D:/opencv/build

# 包含路径
INCLUDEPATH += $$OPENCV_DIR/include
INCLUDEPATH += $$PWD

# 库路径 - OpenCV 4.11.0
CONFIG(debug, debug|release) {
    LIBS += -L$$OPENCV_DIR/x64/vc16/lib \
            -lopencv_world4110d
}

CONFIG(release, debug|release) {
    LIBS += -L$$OPENCV_DIR/x64/vc16/lib \
            -lopencv_world4110
}

# 源文件
SOURCES += \
    main.cpp \
    mainwindow.cpp \
    OpenCVVisionEngine.cpp \
    VideoFrameCapture.cpp \
    GeminiAPIManager.cpp \
    DetectionOverlay.cpp

# 头文件
HEADERS += \
    mainwindow.h \
    OpenCVVisionEngine.h \
    VideoFrameCapture.h \
    GeminiAPIManager.h \
    DetectionOverlay.h

# 资源文件（如果有的话）
# RESOURCES += resources.qrc

# 其他文件（用于在Qt Creator中显示）
OTHER_FILES += \
    models/coco_labels.txt \
    models/YOLO_README.md \
    CMakeLists.txt \
    README.md

# 编译器标志和编码设置
QMAKE_CXXFLAGS += -utf-8
# 确保源文件编码为UTF-8
CODECFORTR = UTF-8
CODECFORSRC = UTF-8
# Qt6中的编码设置
CONFIG += utf8_source

# Windows特定配置
win32 {
    # 设置图标（如果有的话）
    # RC_ICONS = icon.ico
    
    # 设置版本信息
    RC_FILE = version.rc
    
    # 控制台应用程序（用于调试输出）
    CONFIG += console
}

# 预处理器定义
DEFINES += QT_DEPRECATED_WARNINGS

# 禁用特定警告
DEFINES += QT_NO_DEBUG_OUTPUT

# 如果是调试版本，启用调试输出
CONFIG(debug, debug|release) {
    DEFINES -= QT_NO_DEBUG_OUTPUT
    DEFINES += DEBUG_BUILD
}

# 注意：models目录需要手动复制到输出目录
# 或者在代码中使用相对路径 "../../../models"
