#ifndef GEMINIAPIMANAGER_H
#define GEMINIAPIMANAGER_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>
#include <QImage>
#include <QString>
#include <QSettings>

struct GeminiAnalysisResult {
    QString description;
    QString detailedAnalysis;
    QStringList detectedObjects;
    QStringList colors;
    QStringList emotions;
    QString scene;
    bool success;
    QString errorMessage;
    
    GeminiAnalysisResult() : success(false) {}
};

class GeminiAPIManager : public QObject
{
    Q_OBJECT

public:
    explicit GeminiAPIManager(QObject *parent = nullptr);
    ~GeminiAPIManager();

    // API密钥管理
    void setApiKey(const QString& apiKey);
    QString getApiKey() const;
    bool hasValidApiKey() const;
    
    // 图像分析
    void analyzeImage(const QImage& image, const QString& prompt = QString());
    void analyzeImageWithCustomPrompt(const QImage& image, const QString& customPrompt);
    
    // 工具方法
    static QString imageToBase64(const QImage& image);
    static QJsonObject createAnalysisRequest(const QString& base64Image, const QString& prompt);

signals:
    void analysisCompleted(const GeminiAnalysisResult& result);
    void analysisError(const QString& errorMessage);
    void analysisProgress(const QString& status);

private slots:
    void handleNetworkReply();
    void handleNetworkError(QNetworkReply::NetworkError error);

private:
    QNetworkAccessManager* networkManager_;
    QString apiKey_;
    QSettings* settings_;
    
    // API配置
    static const QString API_BASE_URL;
    static const QString API_ENDPOINT;
    static const QString DEFAULT_MODEL;
    
    // 默认提示词
    QString getDefaultPrompt() const;
    QString getDetailedAnalysisPrompt() const;
    
    // 响应处理
    GeminiAnalysisResult parseResponse(const QJsonObject& response);
    QString extractTextFromResponse(const QJsonObject& response);
    
    // 错误处理
    void handleApiError(const QJsonObject& response);
    
    // 设置管理
    void loadSettings();
    void saveSettings();
};

#endif // GEMINIAPIMANAGER_H
