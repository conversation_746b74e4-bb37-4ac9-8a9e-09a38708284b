# OpenCV下载和配置脚本
# 适用于Windows + Qt6 + MinGW

Write-Host "=== OpenCV库下载和配置脚本 ===" -ForegroundColor Green

# 设置下载目录
$downloadDir = "D:\"
$opencvDir = "$downloadDir\opencv"
$opencvVersion = "4.8.0"
$opencvUrl = "https://github.com/opencv/opencv/releases/download/$opencvVersion/opencv-$opencvVersion-windows.exe"

Write-Host "下载目录: $downloadDir" -ForegroundColor Yellow
Write-Host "OpenCV版本: $opencvVersion" -ForegroundColor Yellow

# 检查是否已经存在
if (Test-Path $opencvDir) {
    Write-Host "OpenCV目录已存在: $opencvDir" -ForegroundColor Yellow
    $choice = Read-Host "是否重新下载? (y/n)"
    if ($choice -ne "y") {
        Write-Host "使用现有OpenCV安装" -ForegroundColor Green
        exit 0
    }
}

# 创建下载目录
New-Item -ItemType Directory -Force -Path $downloadDir | Out-Null

# 下载OpenCV
$opencvInstaller = "$downloadDir\opencv-$opencvVersion-windows.exe"

Write-Host "正在下载OpenCV $opencvVersion..." -ForegroundColor Yellow
Write-Host "下载地址: $opencvUrl" -ForegroundColor Cyan

try {
    # 使用Invoke-WebRequest下载
    Invoke-WebRequest -Uri $opencvUrl -OutFile $opencvInstaller -UseBasicParsing
    Write-Host "下载完成!" -ForegroundColor Green
} catch {
    Write-Host "下载失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请手动下载OpenCV:" -ForegroundColor Yellow
    Write-Host "1. 访问: https://opencv.org/releases/" -ForegroundColor Cyan
    Write-Host "2. 下载 opencv-$opencvVersion-windows.exe" -ForegroundColor Cyan
    Write-Host "3. 保存到: $opencvInstaller" -ForegroundColor Cyan
    exit 1
}

# 检查文件是否存在
if (-not (Test-Path $opencvInstaller)) {
    Write-Host "下载的文件不存在: $opencvInstaller" -ForegroundColor Red
    exit 1
}

Write-Host "文件大小: $((Get-Item $opencvInstaller).Length / 1MB) MB" -ForegroundColor Cyan

# 解压OpenCV
Write-Host "正在解压OpenCV..." -ForegroundColor Yellow

try {
    # 运行自解压程序
    Start-Process -FilePath $opencvInstaller -ArgumentList "-o$downloadDir", "-y" -Wait
    Write-Host "解压完成!" -ForegroundColor Green
} catch {
    Write-Host "解压失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请手动运行: $opencvInstaller" -ForegroundColor Yellow
    exit 1
}

# 验证安装
$opencvBuildDir = "$opencvDir\build"
$opencvIncludeDir = "$opencvBuildDir\include"
$opencvLibDir = "$opencvBuildDir\x64\mingw\lib"

Write-Host "验证OpenCV安装..." -ForegroundColor Yellow

if (Test-Path $opencvIncludeDir) {
    Write-Host "✓ 头文件目录存在: $opencvIncludeDir" -ForegroundColor Green
} else {
    Write-Host "✗ 头文件目录不存在: $opencvIncludeDir" -ForegroundColor Red
}

if (Test-Path $opencvLibDir) {
    Write-Host "✓ 库文件目录存在: $opencvLibDir" -ForegroundColor Green
} else {
    Write-Host "✗ 库文件目录不存在: $opencvLibDir" -ForegroundColor Red
    Write-Host "尝试查找其他库目录..." -ForegroundColor Yellow
    
    $possibleLibDirs = @(
        "$opencvBuildDir\x64\vc16\lib",
        "$opencvBuildDir\x64\vc15\lib", 
        "$opencvBuildDir\lib"
    )
    
    foreach ($libDir in $possibleLibDirs) {
        if (Test-Path $libDir) {
            Write-Host "找到库目录: $libDir" -ForegroundColor Green
            $opencvLibDir = $libDir
            break
        }
    }
}

# 显示配置信息
Write-Host "`n=== OpenCV配置信息 ===" -ForegroundColor Green
Write-Host "OpenCV根目录: $opencvDir" -ForegroundColor Cyan
Write-Host "头文件目录: $opencvIncludeDir" -ForegroundColor Cyan
Write-Host "库文件目录: $opencvLibDir" -ForegroundColor Cyan

# 生成CMake配置
$cmakeConfig = @"
# OpenCV配置 - 添加到CMakeLists.txt

# 设置OpenCV路径
set(OpenCV_DIR "$opencvDir/build")
find_package(OpenCV REQUIRED)

# 包含OpenCV头文件
include_directories(`${OpenCV_INCLUDE_DIRS})

# 链接OpenCV库
target_link_libraries(AIVisionPlatform `${OpenCV_LIBS})
"@

$cmakeConfigFile = "opencv_cmake_config.txt"
$cmakeConfig | Out-File -FilePath $cmakeConfigFile -Encoding UTF8

Write-Host "`nCMake配置已保存到: $cmakeConfigFile" -ForegroundColor Green
Write-Host "请将配置添加到CMakeLists.txt中" -ForegroundColor Yellow

Write-Host "`n=== 安装完成 ===" -ForegroundColor Green
Write-Host "下一步: 更新CMakeLists.txt以使用OpenCV" -ForegroundColor Yellow
