# YOLO物体检测模型集成指南

## 概述

本文档描述如何在AI图像识别平台中集成YOLO物体检测功能。

## 目录结构

```
models/
├── coco_labels.txt          # COCO数据集80个类别标签
├── YOLO_README.md          # 本文档
├── yolo/                   # YOLO模型目录
│   ├── model_config.json   # 模型配置文件
│   ├── yolov5/            # YOLOv5模型
│   │   ├── yolov5s.onnx   # YOLOv5s小型模型
│   │   └── yolov5m.onnx   # YOLOv5m中型模型
│   └── yolov8/            # YOLOv8模型
│       ├── yolov8s.onnx   # YOLOv8s小型模型
│       └── yolov8m.onnx   # YOLOv8m中型模型
```

## 模型下载

运行根目录下的PowerShell脚本下载YOLO模型：

```powershell
# 下载所有YOLO模型
.\download_yolo_models.ps1

# 强制重新下载
.\download_yolo_models.ps1 -Force

# 指定下载目录
.\download_yolo_models.ps1 -ModelsDir "D:\MyModels"
```

## 支持的模型

| 模型 | 文件大小 | 推理速度 | 精度 | 推荐用途 |
|------|----------|----------|------|----------|
| YOLOv5s | ~14MB | 快 | 中等 | 实时检测 |
| YOLOv5m | ~42MB | 中等 | 高 | 平衡性能 |
| YOLOv8s | ~22MB | 快 | 高 | 最新快速模型 |
| YOLOv8m | ~52MB | 中等 | 很高 | 最新高精度模型 |

## COCO数据集类别

支持检测80个常见物体类别，包括：
- 人物：person
- 交通工具：car, bicycle, motorcycle, airplane, bus, train, truck, boat
- 动物：bird, cat, dog, horse, sheep, cow, elephant, bear, zebra, giraffe
- 家具：chair, couch, bed, dining table, toilet
- 电子设备：tv, laptop, mouse, remote, keyboard, cell phone
- 食物：banana, apple, sandwich, orange, pizza, donut, cake
- 等等...

完整列表请查看 `coco_labels.txt` 文件。

## 技术规格

### 输入要求
- 图像格式：RGB
- 输入尺寸：640x640像素（自动缩放）
- 颜色空间：RGB (0-255)

### 输出格式
- 边界框：归一化坐标 (0.0-1.0)
- 置信度：0.0-1.0
- 类别ID：0-79 (对应COCO类别)

### 性能参数
- 默认置信度阈值：0.25
- 默认NMS阈值：0.45
- 支持GPU加速（如果可用）

## 集成到OpenCVVisionEngine

### 1. 添加VisionMode枚举
```cpp
enum VisionMode {
    // 现有模式...
    ObjectDetection     // 新增：YOLO物体检测
};
```

### 2. 添加YOLO相关成员变量
```cpp
private:
    void* yoloNet_;                    // cv::dnn::Net*
    std::vector<QString> classNames_;  // COCO类别名称
    float confidenceThreshold_;        // 置信度阈值
    float nmsThreshold_;              // NMS阈值
    int inputSize_;                   // 输入图像尺寸
```

### 3. 实现检测方法
```cpp
std::vector<OpenCVDetection> detectObjects(const QImage& image);
```

## 使用示例

```cpp
// 设置为物体检测模式
openCVEngine->setVisionMode(OpenCVVisionEngine::ObjectDetection);

// 加载YOLO模型
openCVEngine->loadYOLOModel("models/yolo/yolov5/yolov5s.onnx");

// 设置检测参数
openCVEngine->setConfidenceThreshold(0.3f);
openCVEngine->setNMSThreshold(0.4f);

// 开始检测
openCVEngine->setProcessingEnabled(true);
```

## 故障排除

### 常见问题

1. **模型文件未找到**
   - 确保运行了 `download_yolo_models.ps1` 脚本
   - 检查模型文件路径是否正确

2. **OpenCV DNN模块不可用**
   - 确保OpenCV编译时包含了DNN模块
   - 检查OpenCV版本 >= 4.5.0

3. **检测速度慢**
   - 尝试使用更小的模型（如yolov5s）
   - 启用GPU加速（如果支持）
   - 降低输入图像分辨率

4. **检测精度低**
   - 调整置信度阈值
   - 使用更大的模型（如yolov5m或yolov8m）
   - 确保输入图像质量良好

### 性能优化建议

1. **模型选择**
   - 实时应用：使用YOLOv5s或YOLOv8s
   - 高精度需求：使用YOLOv5m或YOLOv8m

2. **参数调优**
   - 置信度阈值：0.2-0.5（根据需求调整）
   - NMS阈值：0.4-0.6（减少重复检测）

3. **硬件加速**
   - 启用OpenCV的GPU支持
   - 使用多线程处理

## 下一步

1. 扩展OpenCVVisionEngine类
2. 实现YOLO推理功能
3. 添加模型管理界面
4. 更新用户界面
5. 性能优化和测试

## 参考资源

- [YOLOv5 官方仓库](https://github.com/ultralytics/yolov5)
- [YOLOv8 官方仓库](https://github.com/ultralytics/ultralytics)
- [OpenCV DNN 文档](https://docs.opencv.org/4.x/d2/d58/tutorial_table_of_content_dnn.html)
- [COCO 数据集](https://cocodataset.org/)
