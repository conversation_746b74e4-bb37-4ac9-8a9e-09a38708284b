{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "D:/QT  pros/111/build/AIVisionPlatform_autogen", "CMAKE_BINARY_DIR": "D:/QT  pros/111/build", "CMAKE_CURRENT_BINARY_DIR": "D:/QT  pros/111/build", "CMAKE_CURRENT_SOURCE_DIR": "D:/QT  pros/111", "CMAKE_EXECUTABLE": "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/bin/cmake.exe", "CMAKE_LIST_FILES": ["D:/QT  pros/111/CMakeLists.txt", "D:/QT  pros/111/build/CMakeFiles/4.0.0/CMakeSystem.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake", "D:/QT  pros/111/build/CMakeFiles/4.0.0/CMakeCCompiler.cmake", "D:/QT  pros/111/build/CMakeFiles/4.0.0/CMakeCXXCompiler.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeGenericSystem.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCInformation.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/MSVC-C.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/MSVC.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-MSVC-C.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-MSVC.cmake", "D:/QT  pros/111/build/CMakeFiles/4.0.0/CMakeRCCompiler.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeRCInformation.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Linker/MSVC-C.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Linker/MSVC.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC-C.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCXXInformation.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/MSVC-CXX.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/MSVC.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-MSVC-CXX.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-MSVC.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Linker/MSVC-CXX.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Linker/MSVC.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC-CXX.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtInstallPaths.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Targets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtFeature.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CheckCXXCompilerFlag.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CheckCompilerFlag.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CheckFlagCommonConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CheckCXXSourceCompiles.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtFeatureCommon.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindThreads.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CheckLibraryExists.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CheckIncludeFile.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageMessage.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/FindWrapAtomic.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CheckCXXSourceCompiles.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageMessage.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateVersionlessAliasTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateVersionlessAliasTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateVersionlessAliasTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfigExtras.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/GNUInstallDirs.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreVersionlessAliasTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsVersionlessTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsVersionlessTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/FindWrapVulkanHeaders.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindVulkan.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageMessage.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageMessage.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsVersionlessTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateVersionlessAliasTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiPlugins.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QGifPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QGifPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QICOPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QICOPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QJpegPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QJpegPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiVersionlessAliasTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateVersionlessAliasTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsMacros.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsPlugins.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsVersionlessAliasTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6MultimediaConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6MultimediaConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6MultimediaConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6MultimediaDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateVersionlessAliasTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkPlugins.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkVersionlessAliasTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6MultimediaTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6MultimediaTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6MultimediaTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6MultimediaAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaPrivate/Qt6MultimediaPrivateConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaPrivate/Qt6MultimediaPrivateConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaPrivate/Qt6MultimediaPrivateConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaPrivate/Qt6MultimediaPrivateDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaPrivate/Qt6MultimediaPrivateTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaPrivate/Qt6MultimediaPrivateAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaPrivate/Qt6MultimediaPrivateVersionlessAliasTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6MultimediaMacros.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6MultimediaPlugins.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6QFFmpegMediaPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6QFFmpegMediaPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6QFFmpegMediaPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6QFFmpegMediaPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6QFFmpegMediaPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6QWindowsMediaPluginConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6QWindowsMediaPluginTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6QWindowsMediaPluginTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6QWindowsMediaPluginTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6QWindowsMediaPluginAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Multimedia/Qt6MultimediaVersionlessAliasTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgets/Qt6MultimediaWidgetsConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgets/Qt6MultimediaWidgetsConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgets/Qt6MultimediaWidgetsConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgets/Qt6MultimediaWidgetsDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgets/Qt6MultimediaWidgetsTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgets/Qt6MultimediaWidgetsTargets-debug.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgets/Qt6MultimediaWidgetsTargets-relwithdebinfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgets/Qt6MultimediaWidgetsAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgetsPrivate/Qt6MultimediaWidgetsPrivateConfigVersion.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgetsPrivate/Qt6MultimediaWidgetsPrivateConfigVersionImpl.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgetsPrivate/Qt6MultimediaWidgetsPrivateConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgetsPrivate/Qt6MultimediaWidgetsPrivateDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgetsPrivate/Qt6MultimediaWidgetsPrivateTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgetsPrivate/Qt6MultimediaWidgetsPrivateAdditionalTargetInfo.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgetsPrivate/Qt6MultimediaWidgetsPrivateVersionlessAliasTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6MultimediaWidgets/Qt6MultimediaWidgetsVersionlessAliasTargets.cmake", "D:/opencv/build/OpenCVConfig-version.cmake", "D:/opencv/build/OpenCVConfig.cmake", "D:/opencv/build/x64/vc16/lib/OpenCVConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake", "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageMessage.cmake", "D:/opencv/build/x64/vc16/lib/OpenCVModules.cmake", "D:/opencv/build/x64/vc16/lib/OpenCVModules-debug.cmake", "D:/opencv/build/x64/vc16/lib/OpenCVModules-release.cmake"], "CMAKE_SOURCE_DIR": "D:/QT  pros/111", "CROSS_CONFIG": false, "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["D:/QT  pros/111/include/DetectionOverlay.h", "Mu", "6YEA5652QU/moc_DetectionOverlay.cpp", null], ["D:/QT  pros/111/include/MainWindow.h", "Mu", "6YEA5652QU/moc_MainWindow.cpp", null], ["D:/QT  pros/111/include/OpenCVVisionEngine.h", "Mu", "6YEA5652QU/moc_OpenCVVisionEngine.cpp", null], ["D:/QT  pros/111/include/VideoFrameCapture.h", "Mu", "6YEA5652QU/moc_VideoFrameCapture.cpp", null], ["D:/QT  pros/111/src/core/GeminiAPIManager.h", "Mu", "PRMOGMWJPH/moc_GeminiAPIManager.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "D:/QT  pros/111/build/AIVisionPlatform_autogen/include", "INCLUDE_DIR_Debug": "D:/QT  pros/111/build/AIVisionPlatform_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "D:/QT  pros/111/build/AIVisionPlatform_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "D:/QT  pros/111/build/AIVisionPlatform_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "D:/QT  pros/111/build/AIVisionPlatform_autogen/include_Release", "MOC_COMPILATION_FILE": "D:/QT  pros/111/build/AIVisionPlatform_autogen/mocs_compilation.cpp", "MOC_COMPILATION_FILE_Debug": "D:/QT  pros/111/build/AIVisionPlatform_autogen/mocs_compilation_Debug.cpp", "MOC_COMPILATION_FILE_MinSizeRel": "D:/QT  pros/111/build/AIVisionPlatform_autogen/mocs_compilation_MinSizeRel.cpp", "MOC_COMPILATION_FILE_RelWithDebInfo": "D:/QT  pros/111/build/AIVisionPlatform_autogen/mocs_compilation_RelWithDebInfo.cpp", "MOC_COMPILATION_FILE_Release": "D:/QT  pros/111/build/AIVisionPlatform_autogen/mocs_compilation_Release.cpp", "MOC_DEFINITIONS": [], "MOC_DEFINITIONS_Debug": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_MULTIMEDIAWIDGETS_LIB", "QT_MULTIMEDIA_LIB", "QT_NETWORK_LIB", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEFINITIONS_MinSizeRel": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_MULTIMEDIAWIDGETS_LIB", "QT_MULTIMEDIA_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEFINITIONS_RelWithDebInfo": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_MULTIMEDIAWIDGETS_LIB", "QT_MULTIMEDIA_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEFINITIONS_Release": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_MULTIMEDIAWIDGETS_LIB", "QT_MULTIMEDIA_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": [], "MOC_INCLUDES_Debug": ["D:/QT  pros/111/include", "D:/opencv/build/include", "D:/QT/6.9.1/msvc2022_64/include/QtCore", "D:/QT/6.9.1/msvc2022_64/include", "D:/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc", "D:/QT/6.9.1/msvc2022_64/include/QtWidgets", "D:/QT/6.9.1/msvc2022_64/include/QtGui", "D:/QT/6.9.1/msvc2022_64/include/QtMultimedia", "D:/QT/6.9.1/msvc2022_64/include/QtNetwork", "D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets"], "MOC_INCLUDES_MinSizeRel": ["D:/QT  pros/111/include", "D:/opencv/build/include", "D:/QT/6.9.1/msvc2022_64/include/QtCore", "D:/QT/6.9.1/msvc2022_64/include", "D:/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc", "D:/QT/6.9.1/msvc2022_64/include/QtWidgets", "D:/QT/6.9.1/msvc2022_64/include/QtGui", "D:/QT/6.9.1/msvc2022_64/include/QtMultimedia", "D:/QT/6.9.1/msvc2022_64/include/QtNetwork", "D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets"], "MOC_INCLUDES_RelWithDebInfo": ["D:/QT  pros/111/include", "D:/opencv/build/include", "D:/QT/6.9.1/msvc2022_64/include/QtCore", "D:/QT/6.9.1/msvc2022_64/include", "D:/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc", "D:/QT/6.9.1/msvc2022_64/include/QtWidgets", "D:/QT/6.9.1/msvc2022_64/include/QtGui", "D:/QT/6.9.1/msvc2022_64/include/QtMultimedia", "D:/QT/6.9.1/msvc2022_64/include/QtNetwork", "D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets"], "MOC_INCLUDES_Release": ["D:/QT  pros/111/include", "D:/opencv/build/include", "D:/QT/6.9.1/msvc2022_64/include/QtCore", "D:/QT/6.9.1/msvc2022_64/include", "D:/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc", "D:/QT/6.9.1/msvc2022_64/include/QtWidgets", "D:/QT/6.9.1/msvc2022_64/include/QtGui", "D:/QT/6.9.1/msvc2022_64/include/QtMultimedia", "D:/QT/6.9.1/msvc2022_64/include/QtNetwork", "D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": true, "PARALLEL": 14, "PARSE_CACHE_FILE": "D:/QT  pros/111/build/CMakeFiles/AIVisionPlatform_autogen.dir/ParseCache.txt", "PARSE_CACHE_FILE_Debug": "D:/QT  pros/111/build/CMakeFiles/AIVisionPlatform_autogen.dir/ParseCache_Debug.txt", "PARSE_CACHE_FILE_MinSizeRel": "D:/QT  pros/111/build/CMakeFiles/AIVisionPlatform_autogen.dir/ParseCache_MinSizeRel.txt", "PARSE_CACHE_FILE_RelWithDebInfo": "D:/QT  pros/111/build/CMakeFiles/AIVisionPlatform_autogen.dir/ParseCache_RelWithDebInfo.txt", "PARSE_CACHE_FILE_Release": "D:/QT  pros/111/build/CMakeFiles/AIVisionPlatform_autogen.dir/ParseCache_Release.txt", "QT_MOC_EXECUTABLE": "", "QT_MOC_EXECUTABLE_Debug": "D:/QT/6.9.1/msvc2022_64/bin/moc.exe", "QT_MOC_EXECUTABLE_MinSizeRel": "D:/QT/6.9.1/msvc2022_64/bin/moc.exe", "QT_MOC_EXECUTABLE_RelWithDebInfo": "D:/QT/6.9.1/msvc2022_64/bin/moc.exe", "QT_MOC_EXECUTABLE_Release": "D:/QT/6.9.1/msvc2022_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 9, "SETTINGS_FILE": "D:/QT  pros/111/build/CMakeFiles/AIVisionPlatform_autogen.dir/AutogenUsed.txt", "SETTINGS_FILE_Debug": "D:/QT  pros/111/build/CMakeFiles/AIVisionPlatform_autogen.dir/AutogenUsed_Debug.txt", "SETTINGS_FILE_MinSizeRel": "D:/QT  pros/111/build/CMakeFiles/AIVisionPlatform_autogen.dir/AutogenUsed_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "D:/QT  pros/111/build/CMakeFiles/AIVisionPlatform_autogen.dir/AutogenUsed_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "D:/QT  pros/111/build/CMakeFiles/AIVisionPlatform_autogen.dir/AutogenUsed_Release.txt", "SOURCES": [["D:/QT  pros/111/src/core/GeminiAPIManager.cpp", "Mu", null], ["D:/QT  pros/111/src/core/OpenCVVisionEngine.cpp", "Mu", null], ["D:/QT  pros/111/src/core/VideoFrameCapture.cpp", "Mu", null], ["D:/QT  pros/111/src/main.cpp", "Mu", null], ["D:/QT  pros/111/src/ui/DetectionOverlay.cpp", "Mu", null], ["D:/QT  pros/111/src/ui/MainWindow.cpp", "Mu", null]], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}