@echo off
echo ========================================
echo     Fix AIVersion Project
echo ========================================
echo.

set "PROJECT_DIR=D:\QTpros\AIVersion"

echo Fixing project files...

REM Backup original file
if exist "%PROJECT_DIR%\AIVersion.pro" (
    copy "%PROJECT_DIR%\AIVersion.pro" "%PROJECT_DIR%\AIVersion.pro.backup" >nul
    echo Backed up original project file
)

REM Create new project file
echo Creating fixed project file...
powershell -Command "& {$content = @'
# Basic Configuration
QT += core gui multimedia multimediawidgets network
greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17
DEFINES += QT_DEPRECATED_WARNINGS

# Encoding Configuration (Fix Chinese encoding issues)
CODECFORTR = UTF-8
CODECFORSRC = UTF-8
CONFIG += utf8_source

# MSVC Compiler encoding configuration
msvc {
    QMAKE_CXXFLAGS += /utf-8
    QMAKE_CFLAGS += /utf-8
}

# GCC Compiler encoding configuration
gcc {
    QMAKE_CXXFLAGS += -finput-charset=UTF-8 -fexec-charset=UTF-8
}

# OpenCV Configuration
OPENCV_DIR = D:/opencv/build
INCLUDEPATH += $$OPENCV_DIR/include

# OpenCV Library linking
CONFIG(debug, debug|release) {
    LIBS += -L$$OPENCV_DIR/x64/vc16/lib -lopencv_world4110d
}
CONFIG(release, debug|release) {
    LIBS += -L$$OPENCV_DIR/x64/vc16/lib -lopencv_world4110
}

# Source Files
SOURCES += \
    main.cpp \
    mainwindow.cpp \
    DetectionOverlay.cpp \
    GeminiAPIManager.cpp \
    OpenCVVisionEngine.cpp \
    VideoFrameCapture.cpp

# Header Files
HEADERS += \
    mainwindow.h \
    DetectionOverlay.h \
    GeminiAPIManager.h \
    OpenCVVisionEngine.h \
    VideoFrameCapture.h

# UI Files
FORMS += \
    mainwindow.ui

# Windows specific configuration
win32 {
    CONFIG += console
}

# Target configuration
TARGET = AIVersion
TEMPLATE = app
'@; [System.IO.File]::WriteAllText('D:\QTpros\AIVersion\AIVersion.pro', $content, [System.Text.Encoding]::UTF8)}"

echo Project file fixed

echo.
echo ========================================
echo Check YOLO Models
echo ========================================

if exist "D:\AI_Models\yolo" (
    echo YOLO model directory exists: D:\AI_Models\yolo
    dir "D:\AI_Models\yolo\*.onnx" /s 2>nul | find ".onnx" >nul
    if %errorlevel% equ 0 (
        echo Found YOLO model files:
        dir "D:\AI_Models\yolo\*.onnx" /s /b
    ) else (
        echo Warning: No YOLO model files found
        echo Please ensure .onnx format YOLO models are in D:\AI_Models\yolo
    )
) else (
    echo YOLO model directory does not exist: D:\AI_Models\yolo
    echo Creating directory...
    mkdir "D:\AI_Models\yolo" 2>nul
    echo Created YOLO model directory
)

echo.
echo ========================================
echo Copy model files to project
echo ========================================

if not exist "%PROJECT_DIR%\models" mkdir "%PROJECT_DIR%\models"

REM Copy COCO labels file
if exist "models\coco_labels.txt" (
    copy "models\coco_labels.txt" "%PROJECT_DIR%\models\" >nul
    echo Copied COCO labels file
) else (
    echo Warning: COCO labels file not found
)

echo.
echo ========================================
echo Fix Complete
echo ========================================
echo.
echo Fixed:
echo 1. Qt project file Chinese encoding configuration
echo 2. Added UTF-8 encoding support
echo 3. Configured OpenCV library linking
echo 4. Checked YOLO model paths
echo.
echo Next steps:
echo 1. Open %PROJECT_DIR%\AIVersion.pro in Qt Creator
echo 2. Rebuild the project
echo 3. If YOLO models are missing, place them in D:\AI_Models\yolo
echo.
echo Press any key to continue...
pause >nul
