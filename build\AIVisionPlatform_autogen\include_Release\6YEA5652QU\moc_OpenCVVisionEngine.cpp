/****************************************************************************
** Meta object code from reading C++ file 'OpenCVVisionEngine.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../include/OpenCVVisionEngine.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'OpenCVVisionEngine.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN18OpenCVVisionEngineE_t {};
} // unnamed namespace

template <> constexpr inline auto OpenCVVisionEngine::qt_create_metaobjectdata<qt_meta_tag_ZN18OpenCVVisionEngineE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "OpenCVVisionEngine",
        "detectionsReady",
        "",
        "std::vector<OpenCVDetection>",
        "detections",
        "processedImage",
        "processingStatusChanged",
        "enabled",
        "errorOccurred",
        "error",
        "statsUpdated",
        "ProcessingStats",
        "stats"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'detectionsReady'
        QtMocHelpers::SignalData<void(const std::vector<OpenCVDetection> &, const QImage &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 4 }, { QMetaType::QImage, 5 },
        }}),
        // Signal 'processingStatusChanged'
        QtMocHelpers::SignalData<void(bool)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 7 },
        }}),
        // Signal 'errorOccurred'
        QtMocHelpers::SignalData<void(const QString &)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 9 },
        }}),
        // Signal 'statsUpdated'
        QtMocHelpers::SignalData<void(const ProcessingStats &)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 11, 12 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<OpenCVVisionEngine, qt_meta_tag_ZN18OpenCVVisionEngineE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject OpenCVVisionEngine::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18OpenCVVisionEngineE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18OpenCVVisionEngineE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN18OpenCVVisionEngineE_t>.metaTypes,
    nullptr
} };

void OpenCVVisionEngine::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<OpenCVVisionEngine *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->detectionsReady((*reinterpret_cast< std::add_pointer_t<std::vector<OpenCVDetection>>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QImage>>(_a[2]))); break;
        case 1: _t->processingStatusChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 2: _t->errorOccurred((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->statsUpdated((*reinterpret_cast< std::add_pointer_t<ProcessingStats>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (OpenCVVisionEngine::*)(const std::vector<OpenCVDetection> & , const QImage & )>(_a, &OpenCVVisionEngine::detectionsReady, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (OpenCVVisionEngine::*)(bool )>(_a, &OpenCVVisionEngine::processingStatusChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (OpenCVVisionEngine::*)(const QString & )>(_a, &OpenCVVisionEngine::errorOccurred, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (OpenCVVisionEngine::*)(const ProcessingStats & )>(_a, &OpenCVVisionEngine::statsUpdated, 3))
            return;
    }
}

const QMetaObject *OpenCVVisionEngine::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *OpenCVVisionEngine::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18OpenCVVisionEngineE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int OpenCVVisionEngine::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void OpenCVVisionEngine::detectionsReady(const std::vector<OpenCVDetection> & _t1, const QImage & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1, _t2);
}

// SIGNAL 1
void OpenCVVisionEngine::processingStatusChanged(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void OpenCVVisionEngine::errorOccurred(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}

// SIGNAL 3
void OpenCVVisionEngine::statsUpdated(const ProcessingStats & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}
QT_WARNING_POP
