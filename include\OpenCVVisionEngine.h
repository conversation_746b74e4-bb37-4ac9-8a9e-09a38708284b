#pragma once

#include <QObject>
#include <QImage>
#include <QString>
#include <QStringList>
#include <QRectF>
#include <QTimer>
#include <QColor>
#include <vector>
#include <memory>

// OpenCV头文件
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/objdetect.hpp>
#include <opencv2/video/background_segm.hpp>
#include <opencv2/dnn.hpp>

/**
 * @brief OpenCV检测结果
 */
struct OpenCVDetection {
    QString type;           // 检测类型 (face, motion, edge, etc.)
    QString description;    // 描述信息
    float confidence;       // 置信度
    QRectF boundingBox;     // 边界框 (归一化坐标)
    QColor color;          // 显示颜色
    
    OpenCVDetection() : confidence(0.0f) {}
    OpenCVDetection(const QString& t, const QString& desc, float conf, const QRectF& box, const QColor& c = Qt::red)
        : type(t), description(desc), confidence(conf), boundingBox(box), color(c) {}
};

/**
 * @brief OpenCV计算机视觉引擎
 * 
 * 使用OpenCV内置算法进行实时图像分析
 */
class OpenCVVisionEngine : public QObject {
    Q_OBJECT

public:
    enum VisionMode {
        FaceDetection,      // 人脸检测
        MotionDetection,    // 运动检测
        EdgeDetection,      // 边缘检测
        ColorDetection,     // 颜色检测
        FeatureDetection,   // 特征点检测
        ObjectTracking,     // 物体跟踪
        ContourDetection,   // 轮廓检测
        ObjectDetection     // YOLO物体检测
    };

    explicit OpenCVVisionEngine(QObject* parent = nullptr);
    ~OpenCVVisionEngine();

    /**
     * @brief 设置视觉模式
     */
    void setVisionMode(VisionMode mode);
    VisionMode getVisionMode() const { return currentMode_; }

    /**
     * @brief 获取可用的视觉模式列表
     */
    static QStringList getAvailableModes();

    /**
     * @brief 设置是否启用处理
     */
    void setProcessingEnabled(bool enabled);
    bool isProcessingEnabled() const { return processingEnabled_; }

    /**
     * @brief 处理单帧图像
     */
    std::vector<OpenCVDetection> processFrame(const QImage& image);

    /**
     * @brief 设置检测参数
     */
    void setDetectionThreshold(float threshold) { detectionThreshold_ = threshold; }
    void setMinObjectSize(int size) { minObjectSize_ = size; }
    void setMaxObjectSize(int size) { maxObjectSize_ = size; }

    /**
     * @brief YOLO模型管理
     */
    bool loadYOLOModel(const QString& modelPath);
    void setYOLOConfidenceThreshold(float threshold) { yoloConfidenceThreshold_ = threshold; }
    void setYOLONMSThreshold(float threshold) { yoloNMSThreshold_ = threshold; }
    QStringList getAvailableYOLOModels() const;
    QString getCurrentYOLOModel() const { return currentYOLOModelPath_; }

    /**
     * @brief 获取处理统计信息
     */
    struct ProcessingStats {
        int totalFrames = 0;
        int processedFrames = 0;
        float averageProcessingTime = 0.0f;
        int detectionsCount = 0;
    };
    ProcessingStats getStats() const { return stats_; }

    /**
     * @brief 重置统计信息
     */
    void resetStats();

    /**
     * @brief 获取当前模式的描述
     */
    QString getCurrentModeDescription() const;

signals:
    /**
     * @brief 检测结果就绪
     */
    void detectionsReady(const std::vector<OpenCVDetection>& detections, const QImage& processedImage);

    /**
     * @brief 处理状态改变
     */
    void processingStatusChanged(bool enabled);

    /**
     * @brief 错误信号
     */
    void errorOccurred(const QString& error);

    /**
     * @brief 统计信息更新
     */
    void statsUpdated(const ProcessingStats& stats);

private:
    VisionMode currentMode_;
    bool processingEnabled_;
    
    // 检测参数
    float detectionThreshold_;
    int minObjectSize_;
    int maxObjectSize_;
    
    // 统计信息
    mutable ProcessingStats stats_;
    
    // OpenCV相关 (使用void*避免包含OpenCV头文件)
    void* faceClassifier_;      // cv::CascadeClassifier*
    void* backgroundSubtractor_; // cv::BackgroundSubtractor*
    void* tracker_;             // cv::Tracker*

    // YOLO相关
    void* yoloNet_;             // cv::dnn::Net*
    std::vector<QString> yoloClassNames_;  // COCO类别名称
    QString currentYOLOModelPath_;         // 当前YOLO模型路径
    float yoloConfidenceThreshold_;        // YOLO置信度阈值
    float yoloNMSThreshold_;              // YOLO NMS阈值
    int yoloInputSize_;                   // YOLO输入图像尺寸
    
    // 处理方法
    std::vector<OpenCVDetection> detectFaces(const QImage& image);
    std::vector<OpenCVDetection> detectMotion(const QImage& image);
    std::vector<OpenCVDetection> detectEdges(const QImage& image);
    std::vector<OpenCVDetection> detectColors(const QImage& image);
    std::vector<OpenCVDetection> detectFeatures(const QImage& image);
    std::vector<OpenCVDetection> trackObjects(const QImage& image);
    std::vector<OpenCVDetection> detectContours(const QImage& image);
    std::vector<OpenCVDetection> detectYOLOObjects(const QImage& image);
    
    // 工具方法
    void* qImageToCvMat(const QImage& qimg);
    QImage cvMatToQImage(const void* mat);
    void initializeClassifiers();
    void updateStats(float processingTime);

    // YOLO工具方法
    bool initializeYOLO();
    bool loadYOLOClassNames(const QString& labelsPath);
    cv::Mat preprocessYOLOImage(const cv::Mat& image);
    std::vector<OpenCVDetection> postprocessYOLOOutput(const std::vector<cv::Mat>& outputs,
                                                       const cv::Size& imageSize);
    std::vector<OpenCVDetection> simulateYOLODetection(const QImage& image);
};
