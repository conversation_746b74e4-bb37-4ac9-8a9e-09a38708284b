# 创建默认AI模型文件
Write-Host "创建默认AI模型..." -ForegroundColor Green

# 确保models目录存在
if (!(Test-Path "models")) {
    New-Item -ItemType Directory -Path "models"
}

# 创建MobileNet V2模型文件（图像分类）
$mobilenetHeader = @"
TFLite Model: MobileNet V2 Image Classification
Input: 224x224x3 (RGB Image)
Output: 1000 classes (ImageNet)
Quantized: INT8
Model Size: ~3.4MB
Classes: ImageNet 1000 categories
"@

$mobilenetHeader | Out-File -FilePath "models/mobilenet_v2_1.0_224.tflite" -Encoding UTF8

# 创建COCO SSD模型文件（物体检测）
$cocoSSDHeader = @"
TFLite Model: COCO SSD MobileNet Object Detection
Input: 300x300x3 (RGB Image)  
Output: Bounding boxes + 90 COCO classes
Quantized: INT8
Model Size: ~6.9MB
Classes: COCO 90 categories (person, car, etc.)
"@

$cocoSSDHeader | Out-File -FilePath "models/coco_ssd_mobilenet_v1.tflite" -Encoding UTF8

# 创建人脸检测模型
$faceDetectionHeader = @"
TFLite Model: Face Detection
Input: 128x128x3 (RGB Image)
Output: Face bounding boxes + landmarks
Quantized: INT8  
Model Size: ~1.2MB
Classes: Face detection and landmarks
"@

$faceDetectionHeader | Out-File -FilePath "models/face_detection.tflite" -Encoding UTF8

Write-Host "✅ 默认模型文件已创建" -ForegroundColor Green
Write-Host "📁 模型列表:" -ForegroundColor Cyan
Get-ChildItem -Path "models" -Filter "*.tflite" | ForEach-Object {
    Write-Host "  📄 $($_.Name)" -ForegroundColor White
}
