^D:\QT  PROS\111\BUILD\CMAKEFILES\9456881245D34B74B7F2D32856B2ADC1\GENERATE.STAMP.RULE
setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\bin\cmake.exe "-SD:/QT  pros/111" "-BD:/QT  pros/111/build" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "D:/QT  pros/111/build/AIVisionPlatform.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
