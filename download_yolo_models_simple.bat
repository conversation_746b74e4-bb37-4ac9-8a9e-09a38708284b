@echo off
chcp 65001 >nul
echo ========================================
echo     YOLO模型下载脚本
echo ========================================
echo.

REM 创建模型目录
if not exist "models" mkdir "models"
if not exist "D:\AI_Models\yolo" mkdir "D:\AI_Models\yolo"

echo 正在下载YOLOv5s模型...
echo.

REM 下载YOLOv5s ONNX模型 (约14MB)
echo 下载YOLOv5s ONNX模型到项目models目录...
powershell -Command "& {try { Invoke-WebRequest -Uri 'https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5s.onnx' -OutFile 'models\yolov5s.onnx' -UseBasicParsing; Write-Host '✓ YOLOv5s模型下载成功' -ForegroundColor Green } catch { Write-Host '✗ YOLOv5s模型下载失败' -ForegroundColor Red; Write-Host $_.Exception.Message }}"

echo.
echo 下载YOLOv5s ONNX模型到标准AI模型目录...
powershell -Command "& {try { Invoke-WebRequest -Uri 'https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5s.onnx' -OutFile 'D:\AI_Models\yolo\yolov5s.onnx' -UseBasicParsing; Write-Host '✓ YOLOv5s模型下载成功' -ForegroundColor Green } catch { Write-Host '✗ YOLOv5s模型下载失败' -ForegroundColor Red; Write-Host $_.Exception.Message }}"

echo.
echo ========================================
echo 检查下载结果...
echo ========================================

if exist "models\yolov5s.onnx" (
    echo ✓ 项目models目录中的YOLOv5s模型存在
    for %%A in ("models\yolov5s.onnx") do echo   文件大小: %%~zA 字节
) else (
    echo ✗ 项目models目录中的YOLOv5s模型不存在
)

if exist "D:\AI_Models\yolo\yolov5s.onnx" (
    echo ✓ 标准目录中的YOLOv5s模型存在
    for %%A in ("D:\AI_Models\yolo\yolov5s.onnx") do echo   文件大小: %%~zA 字节
) else (
    echo ✗ 标准目录中的YOLOv5s模型不存在
)

echo.
echo ========================================
echo 备用下载方案
echo ========================================
echo 如果自动下载失败，请手动下载：
echo.
echo 1. 访问: https://github.com/ultralytics/yolov5/releases/tag/v7.0
echo 2. 下载 yolov5s.onnx 文件
echo 3. 将文件放置到以下任一目录：
echo    - %CD%\models\
echo    - D:\AI_Models\yolo\
echo.

echo 下载完成！按任意键继续...
pause >nul
