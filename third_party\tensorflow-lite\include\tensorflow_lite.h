﻿#pragma once

// TensorFlow Lite C++ API 澶存枃浠?// 绠€鍖栫増鏈紝鐢ㄤ簬婕旂ず

#include <vector>
#include <string>
#include <memory>

namespace tflite {
    
    enum TfLiteStatus {
        kTfLiteOk = 0,
        kTfLiteError = 1
    };
    
    class FlatBufferModel {
    public:
        static std::unique_ptr<FlatBufferModel> BuildFromFile(const char* filename);
        static std::unique_ptr<FlatBufferModel> BuildFromBuffer(const char* buffer, size_t buffer_size);
    };
    
    class Interpreter {
    public:
        TfLiteStatus AllocateTensors();
        TfLiteStatus Invoke();
        void SetNumThreads(int num_threads);
        
        template<typename T>
        T* typed_input_tensor(int tensor_index);
        
        template<typename T>
        T* typed_output_tensor(int tensor_index);
        
        struct TfLiteTensor* input_tensor(int tensor_index);
        struct TfLiteTensor* output_tensor(int tensor_index);
        
        size_t inputs_size() const;
        size_t outputs_size() const;
    };
    
    namespace ops {
        namespace builtin {
            class BuiltinOpResolver {
            public:
                BuiltinOpResolver();
            };
        }
    }
    
    class InterpreterBuilder {
    public:
        InterpreterBuilder(const FlatBufferModel& model, const ops::builtin::BuiltinOpResolver& resolver);
        TfLiteStatus operator()(std::unique_ptr<Interpreter>* interpreter);
    };
    
    struct TfLiteTensor {
        int bytes;
        std::vector<int> dims;
    };
}
