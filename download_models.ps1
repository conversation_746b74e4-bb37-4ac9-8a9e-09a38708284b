# 下载真实的TensorFlow Lite模型
Write-Host "=== 下载TensorFlow Lite模型 ===" -ForegroundColor Green

# 确保models目录存在
if (!(Test-Path "models")) {
    New-Item -ItemType Directory -Path "models"
}

# 模型下载列表
$models = @(
    @{
        Name = "MobileNet V2 (图像分类)"
        Url = "https://storage.googleapis.com/download.tensorflow.org/models/tflite/mobilenet_v1_1.0_224_quant.tflite"
        File = "mobilenet_v1_224_quant.tflite"
        Description = "1000类图像分类模型，输入224x224"
    },
    @{
        Name = "COCO SSD MobileNet (物体检测)"
        Url = "https://storage.googleapis.com/download.tensorflow.org/models/tflite/coco_ssd_mobilenet_v1_1.0_quant_2018_06_29.zip"
        File = "coco_ssd_mobilenet.zip"
        Description = "90类物体检测模型，输入300x300"
    },
    @{
        Name = "Pose Estimation (姿态估计)"
        Url = "https://storage.googleapis.com/download.tensorflow.org/models/tflite/posenet_mobilenet_v1_100_257x257_multi_kpt_stripped.tflite"
        File = "posenet_mobilenet.tflite"
        Description = "人体姿态估计模型，输入257x257"
    }
)

foreach ($model in $models) {
    $outputPath = "models/$($model.File)"
    
    if (Test-Path $outputPath) {
        Write-Host "✅ $($model.Name) 已存在" -ForegroundColor Green
        continue
    }
    
    Write-Host "📥 下载 $($model.Name)..." -ForegroundColor Yellow
    Write-Host "   URL: $($model.Url)" -ForegroundColor Gray
    
    try {
        # 使用.NET WebClient下载
        $webClient = New-Object System.Net.WebClient
        $webClient.DownloadFile($model.Url, $outputPath)
        
        if (Test-Path $outputPath) {
            $size = (Get-Item $outputPath).Length / 1MB
            Write-Host "✅ 下载完成: $($model.File) (${size:F1} MB)" -ForegroundColor Green
        } else {
            Write-Host "❌ 下载失败: $($model.File)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ 下载错误: $($_.Exception.Message)" -ForegroundColor Red
        
        # 创建占位文件用于测试
        "TensorFlow Lite Model Placeholder - $($model.Name)" | Out-File -FilePath $outputPath -Encoding UTF8
        Write-Host "📝 已创建占位文件用于测试" -ForegroundColor Yellow
    }
}

# 处理压缩文件
if (Test-Path "models/coco_ssd_mobilenet.zip") {
    Write-Host "📦 解压COCO SSD模型..." -ForegroundColor Yellow
    try {
        Expand-Archive -Path "models/coco_ssd_mobilenet.zip" -DestinationPath "models/coco_ssd" -Force
        
        # 查找.tflite文件
        $tfliteFile = Get-ChildItem -Path "models/coco_ssd" -Filter "*.tflite" -Recurse | Select-Object -First 1
        if ($tfliteFile) {
            Copy-Item $tfliteFile.FullName "models/coco_ssd_mobilenet.tflite"
            Write-Host "✅ COCO SSD模型已提取" -ForegroundColor Green
        }
        
        # 清理
        Remove-Item "models/coco_ssd_mobilenet.zip"
        Remove-Item "models/coco_ssd" -Recurse -Force
    }
    catch {
        Write-Host "❌ 解压失败，使用占位文件" -ForegroundColor Red
    }
}

# 创建模型信息文件
$modelInfo = @"
# TensorFlow Lite 模型信息

## 可用模型

### 1. MobileNet V2 (默认)
- **文件**: mobilenet_v1_224_quant.tflite
- **类型**: 图像分类
- **输入**: 224x224x3
- **输出**: 1000个类别的概率
- **用途**: 识别日常物品、动物、食物等

### 2. COCO SSD MobileNet
- **文件**: coco_ssd_mobilenet.tflite  
- **类型**: 物体检测
- **输入**: 300x300x3
- **输出**: 边界框 + 90个COCO类别
- **用途**: 检测人、车、动物等物体位置

### 3. PoseNet MobileNet
- **文件**: posenet_mobilenet.tflite
- **类型**: 姿态估计
- **输入**: 257x257x3
- **输出**: 17个关键点坐标
- **用途**: 人体姿态分析

## 使用方法

1. 程序启动时自动加载默认模型(MobileNet V2)
2. 通过"文件 → 加载AI模型"切换其他模型
3. 不同模型有不同的识别效果

## COCO数据集类别

person, bicycle, car, motorcycle, airplane, bus, train, truck, boat,
traffic light, fire hydrant, stop sign, parking meter, bench, bird,
cat, dog, horse, sheep, cow, elephant, bear, zebra, giraffe, backpack,
umbrella, handbag, tie, suitcase, frisbee, skis, snowboard, sports ball,
kite, baseball bat, baseball glove, skateboard, surfboard, tennis racket,
bottle, wine glass, cup, fork, knife, spoon, bowl, banana, apple,
sandwich, orange, broccoli, carrot, hot dog, pizza, donut, cake, chair,
couch, potted plant, bed, dining table, toilet, tv, laptop, mouse,
remote, keyboard, cell phone, microwave, oven, toaster, sink,
refrigerator, book, clock, vase, scissors, teddy bear, hair drier,
toothbrush
"@

$modelInfo | Out-File -FilePath "models/README.md" -Encoding UTF8

Write-Host "`n=== 下载完成 ===" -ForegroundColor Green
Write-Host "📁 模型文件位置: models/" -ForegroundColor Cyan
Get-ChildItem -Path "models" -Filter "*.tflite" | ForEach-Object {
    $size = $_.Length / 1KB
    Write-Host "  📄 $($_.Name) (${size:F0} KB)" -ForegroundColor White
}

Write-Host "`n🚀 接下来:" -ForegroundColor Yellow
Write-Host "1. 重新编译项目以包含默认模型" -ForegroundColor White
Write-Host "2. 程序将自动加载MobileNet V2作为默认模型" -ForegroundColor White
Write-Host "3. 用户可以通过菜单切换其他模型" -ForegroundColor White
