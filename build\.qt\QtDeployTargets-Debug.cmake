set(__QT_DEPLOY_TARGET_AIVisionPlatform_FILE D:/QT  pros/111/build/bin/Debug/AIVisionPlatform.exe)
set(__QT_DEPLOY_TARGET_AIVisionPlatform_TYPE EXECUTABLE)
set(__QT_DEPLOY_TARGET_AIVisionPlatform_RUNTIME_DLLS D:/QT/6.9.1/msvc2022_64/bin/Qt6MultimediaWidgetsd.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/opencv/build/x64/vc16/bin/opencv_world4110d.dll;D:/QT/6.9.1/msvc2022_64/bin/Qt6Widgetsd.dll;D:/QT/6.9.1/msvc2022_64/bin/Qt6Multimediad.dll;D:/QT/6.9.1/msvc2022_64/bin/Qt6Networkd.dll;D:/QT/6.9.1/msvc2022_64/bin/Qt6Guid.dll;D:/QT/6.9.1/msvc2022_64/bin/Qt6Cored.dll)
