#include "../../include/VideoFrameCapture.h"
#include <QDebug>
#include <QVideoFrame>
#include <QImage>
#include <QFile>
#include <QTextStream>
#include <QStringConverter>

VideoFrameCapture::VideoFrameCapture(QObject* parent)
    : QObject(parent)
    , videoSink_(nullptr)
    , enabled_(false)
    , hasFrame_(false)
{
    qDebug() << "VideoFrameCapture initialized";
}

VideoFrameCapture::~VideoFrameCapture() {
    if (videoSink_) {
        disconnect(videoSink_, nullptr, this, nullptr);
    }
    qDebug() << "VideoFrameCapture destroyed";
}

void VideoFrameCapture::setVideoSink(QVideoSink* sink) {
    if (videoSink_) {
        disconnect(videoSink_, nullptr, this, nullptr);
    }
    
    videoSink_ = sink;
    
    if (videoSink_) {
        connect(videoSink_, &QVideoSink::videoFrameChanged,
                this, &VideoFrameCapture::onVideoFrameChanged);
        qDebug() << "VideoSink connected for frame capture";
    }
}

QImage VideoFrameCapture::getLatestFrame() const {
    return latestFrame_;
}

bool VideoFrameCapture::hasFrame() const {
    return hasFrame_;
}

void VideoFrameCapture::setEnabled(bool enabled) {
    if (enabled_ != enabled) {
        enabled_ = enabled;
        emit captureStatusChanged(enabled);
        qDebug() << "Frame capture" << (enabled ? "enabled" : "disabled");
    }
}

void VideoFrameCapture::onVideoFrameChanged(const QVideoFrame& frame) {
    if (!enabled_ || !frame.isValid()) {
        return;
    }

    try {
        QImage image = videoFrameToQImage(frame);
        if (!image.isNull()) {
            latestFrame_ = image;
            hasFrame_ = true;
            emit frameAvailable(image);

            // 每100帧输出一次调试信息
            static int frameCount = 0;
            if (++frameCount % 100 == 0) {
                qDebug() << "Captured frame" << frameCount
                         << "Size:" << image.size()
                         << "Format:" << image.format();
            }
        }
    } catch (const std::exception& e) {
        qDebug() << "Error capturing video frame:" << e.what();
    }
}

void VideoFrameCapture::setDetections(const std::vector<Detection>& detections) {
    detections_ = detections;
}

QImage VideoFrameCapture::drawDetectionsOnFrame(const QImage& frame, const std::vector<Detection>& detections) {
    if (detections.empty()) {
        return frame;
    }

    QImage result = frame.copy();
    QPainter painter(&result);
    painter.setRenderHint(QPainter::Antialiasing);

    for (const auto& detection : detections) {
        // 将相对坐标转换为绝对坐标
        QRectF relativeBox = detection.boundingBox;

        // 确保坐标在有效范围内 (0-1)
        float x = qBound(0.0f, (float)relativeBox.x(), 1.0f);
        float y = qBound(0.0f, (float)relativeBox.y(), 1.0f);
        float w = qBound(0.0f, (float)relativeBox.width(), 1.0f - x);
        float h = qBound(0.0f, (float)relativeBox.height(), 1.0f - y);

        QRectF absoluteBox(
            x * result.width(),
            y * result.height(),
            w * result.width(),
            h * result.height()
        );

        qDebug() << "Drawing detection at relative:" << QRectF(x, y, w, h)
                 << "absolute:" << absoluteBox
                 << "image size:" << result.size();

        // 绘制边界框 - 使用鲜艳的红色
        QPen boxPen(QColor(255, 0, 0), 8); // 红色，8像素粗
        painter.setPen(boxPen);
        painter.setBrush(Qt::NoBrush);
        painter.drawRect(absoluteBox);

        // 绘制标签
        QString labelText = detection.className + QString(" (%1%)").arg(QString::number(detection.confidence * 100, 'f', 1));

        QFont font("Arial", 16, QFont::Bold);
        painter.setFont(font);
        QFontMetrics fm(font);
        QRect textRect = fm.boundingRect(labelText);

        // 标签背景
        QRectF labelBgRect(
            absoluteBox.x(),
            absoluteBox.y() - textRect.height() - 10,
            textRect.width() + 20,
            textRect.height() + 10
        );

        // 确保标签在图像内
        if (labelBgRect.y() < 0) {
            labelBgRect.moveTop(absoluteBox.bottom());
        }

        // 绘制标签背景
        painter.setBrush(QColor(255, 0, 0, 200)); // 红色背景
        painter.setPen(QPen(QColor(255, 255, 255), 2)); // 白色边框
        painter.drawRect(labelBgRect);

        // 绘制标签文字
        painter.setPen(QColor(255, 255, 255)); // 白色文字
        painter.drawText(
            labelBgRect.x() + 10,
            labelBgRect.y() + fm.ascent() + 5,
            labelText
        );

        qDebug() << "Drew detection on frame:" << labelText << "at" << absoluteBox;
    }

    return result;
}

QImage VideoFrameCapture::videoFrameToQImage(const QVideoFrame& frame) {
    if (!frame.isValid()) {
        return QImage();
    }
    
    // 创建可映射的帧副本
    QVideoFrame mappableFrame = frame;
    if (!mappableFrame.map(QVideoFrame::ReadOnly)) {
        qDebug() << "Failed to map video frame";
        return QImage();
    }
    
    QImage image;
    
    // 对于所有格式，直接使用Qt的内置转换
    // Qt6的toImage()方法已经能够处理大多数格式
    image = mappableFrame.toImage();

    if (image.isNull()) {
        qDebug() << "Failed to convert video frame to image, pixel format:"
                 << mappableFrame.pixelFormat();
    } else {
        // 输出格式信息用于调试
        static bool formatLogged = false;
        if (!formatLogged) {
            qDebug() << "Video frame format:" << mappableFrame.pixelFormat()
                     << "Size:" << mappableFrame.size()
                     << "Converted to QImage format:" << image.format();
            formatLogged = true;
        }
    }
    
    mappableFrame.unmap();
    
    // 确保图像格式为RGB888，便于OpenCV处理
    if (!image.isNull() && image.format() != QImage::Format_RGB888) {
        image = image.convertToFormat(QImage::Format_RGB888);
    }
    
    return image;
}
