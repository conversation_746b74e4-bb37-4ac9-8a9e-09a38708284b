[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-utf-8", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.41", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DDEBUG_BUILD", "-DQT_QML_DEBUG", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\QT  pros\\111", "-ID:\\opencv\\build\\include", "-ID:\\QT\\6.9.1\\msvc2022_64\\include", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtMultimediaWidgets", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtMultimedia", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\QT  pros\\111\\build\\debug\\moc", "-ID:\\QT  pros\\111\\include", "-ID:\\QT  pros\\111\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-ID:\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:D:\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\include", "/clang:-isystem", "/clang:D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\VS2\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\QT  pros\\111\\mainwindow.h"], "directory": "D:/QT  pros/111/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/QT  pros/111/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-utf-8", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.41", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DDEBUG_BUILD", "-DQT_QML_DEBUG", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\QT  pros\\111", "-ID:\\opencv\\build\\include", "-ID:\\QT\\6.9.1\\msvc2022_64\\include", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtMultimediaWidgets", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtMultimedia", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\QT  pros\\111\\build\\debug\\moc", "-ID:\\QT  pros\\111\\include", "-ID:\\QT  pros\\111\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-ID:\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:D:\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\include", "/clang:-isystem", "/clang:D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\VS2\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "objective-c++-header", "D:\\QT  pros\\111\\mainwindow.h"], "directory": "D:/QT  pros/111/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/QT  pros/111/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:strictStrings", "-<PERSON>i", "-MDd", "-utf-8", "-W3", "-w44456", "-w44457", "-w44458", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "/clang:-std=c11", "-fms-compatibility-version=19.41", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DDEBUG_BUILD", "-DQT_QML_DEBUG", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\QT  pros\\111", "-ID:\\opencv\\build\\include", "-ID:\\QT\\6.9.1\\msvc2022_64\\include", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtMultimediaWidgets", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtMultimedia", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\QT  pros\\111\\build\\debug\\moc", "-ID:\\QT  pros\\111\\include", "-ID:\\QT  pros\\111\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-ID:\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:D:\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\include", "/clang:-isystem", "/clang:D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\VS2\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TC", "D:\\QT  pros\\111\\mainwindow.h"], "directory": "D:/QT  pros/111/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/QT  pros/111/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:strictStrings", "-<PERSON>i", "-MDd", "-utf-8", "-W3", "-w44456", "-w44457", "-w44458", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "/clang:-std=c11", "-fms-compatibility-version=19.41", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DDEBUG_BUILD", "-DQT_QML_DEBUG", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\QT  pros\\111", "-ID:\\opencv\\build\\include", "-ID:\\QT\\6.9.1\\msvc2022_64\\include", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtMultimediaWidgets", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtMultimedia", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-ID:\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\QT  pros\\111\\build\\debug\\moc", "-ID:\\QT  pros\\111\\include", "-ID:\\QT  pros\\111\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-ID:\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:D:\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\include", "/clang:-isystem", "/clang:D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\VS2\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "objective-c-header", "D:\\QT  pros\\111\\mainwindow.h"], "directory": "D:/QT  pros/111/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/QT  pros/111/mainwindow.h"}]