D:/QT\ \ pros/111/build/AIVisionPlatform_autogen/include_Release/6YEA5652QU/moc_MainWindow.cpp: D:/QT\ \ pros/111/include/MainWindow.h \
  D:/QT\ \ pros/111/include/DetectionOverlay.h \
  D:/QT\ \ pros/111/include/OpenCVVisionEngine.h \
  D:/QT\ \ pros/111/include/VideoFrameCapture.h \
  D:/QT\ \ pros/111/src/core/GeminiAPIManager.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QDateTime \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QDir \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QFileInfo \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QFlags \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QIODevice \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonArray \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonDocument \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonObject \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QList \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QMap \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QMetaType \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QObject \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QRectF \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QSettings \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QSharedDataPointer \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QStandardPaths \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QString \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QStringList \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QTimer \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QUrl \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/QVariant \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q26numeric.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qabstracteventdispatcher.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qabstractitemmodel.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasictimer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcalendar.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborcommon.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborvalue.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontiguouscache.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcoreapplication.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcoreapplication_platform.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcoreevent.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcryptographichash.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatetime.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qdeadlinetimer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qdebug.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qdir.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qdirlisting.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qelapsedtimer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qendian.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qeventloop.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qfile.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qfiledevice.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qfileinfo.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qhash.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevice.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qitemselectionmodel.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonarray.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsondocument.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonobject.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonparseerror.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonvalue.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qline.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qlocale.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qmap.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qnativeinterface.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qregularexpression.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qset.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qsettings.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstandardpaths.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtextstream.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtimer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtimezone.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qurl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/quuid.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qvariant.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qvarlengtharray.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/QClipboard \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/QColor \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/QFont \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/QFontMetrics \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/QImage \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/QPainter \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/QPixmap \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qaction.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qbitmap.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qbrush.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qclipboard.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qcursor.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qfont.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontinfo.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontmetrics.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontvariableaxis.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qguiapplication.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qguiapplication_platform.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qicon.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qinputmethod.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qkeysequence.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qpainter.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qpalette.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qpen.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qpicture.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixmap.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qtextcursor.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qtextdocument.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qtextformat.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qtextoption.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qvalidator.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QCamera \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QCameraDevice \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QImageCapture \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QMediaCaptureSession \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QMediaDevices \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QMediaRecorder \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QVideoFrame \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QVideoSink \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qcamera.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qcameradevice.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qimagecapture.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qmediacapturesession.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qmediadevices.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qmediametadata.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qmediarecorder.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qtmultimedia-config.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qtmultimediaexports.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qtmultimediaglobal.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qtvideo.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qvideoframe.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qvideoframeformat.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qvideosink.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets/QVideoWidget \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets/qtmultimediawidgetsexports.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets/qtmultimediawidgetsglobal.h \
  D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets/qvideowidget.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QNetworkAccessManager \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QNetworkReply \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QNetworkRequest \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QSslConfiguration \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qabstractsocket.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qhostaddress.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qhttpheaders.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qnetworkaccessmanager.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qnetworkreply.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qnetworkrequest.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qssl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslcertificate.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslconfiguration.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslerror.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslsocket.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtcpsocket.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtnetwork-config.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtnetworkexports.h \
  D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtnetworkglobal.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QApplication \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QDialog \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QFileDialog \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QFormLayout \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QFrame \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QGroupBox \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QHBoxLayout \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QInputDialog \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QLabel \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QLayout \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QListWidget \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QMainWindow \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QMenuBar \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QMessageBox \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QProgressDialog \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QPushButton \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QScrollArea \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QSlider \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QStatusBar \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QTextEdit \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QVBoxLayout \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QWidget \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractbutton.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractitemdelegate.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractitemview.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractscrollarea.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractslider.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractspinbox.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qapplication.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qboxlayout.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qdialog.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qdialogbuttonbox.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qfiledialog.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qformlayout.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qframe.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qgridlayout.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qgroupbox.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qinputdialog.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlabel.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlayout.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlayoutitem.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlineedit.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlistview.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlistwidget.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qmainwindow.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qmenu.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qmenubar.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qmessagebox.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qprogressdialog.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qpushbutton.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qrubberband.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qscrollarea.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qsizepolicy.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qslider.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qstatusbar.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qstyle.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qstyleoption.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtabbar.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtabwidget.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtextedit.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgets-config.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsexports.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qwidget.h \
  D:/opencv/build/include/opencv2/calib3d.hpp \
  D:/opencv/build/include/opencv2/core.hpp \
  D:/opencv/build/include/opencv2/core/affine.hpp \
  D:/opencv/build/include/opencv2/core/async.hpp \
  D:/opencv/build/include/opencv2/core/base.hpp \
  D:/opencv/build/include/opencv2/core/bufferpool.hpp \
  D:/opencv/build/include/opencv2/core/check.hpp \
  D:/opencv/build/include/opencv2/core/cuda.hpp \
  D:/opencv/build/include/opencv2/core/cuda.inl.hpp \
  D:/opencv/build/include/opencv2/core/cuda_types.hpp \
  D:/opencv/build/include/opencv2/core/cv_cpu_dispatch.h \
  D:/opencv/build/include/opencv2/core/cvdef.h \
  D:/opencv/build/include/opencv2/core/cvstd.hpp \
  D:/opencv/build/include/opencv2/core/cvstd.inl.hpp \
  D:/opencv/build/include/opencv2/core/cvstd_wrapper.hpp \
  D:/opencv/build/include/opencv2/core/fast_math.hpp \
  D:/opencv/build/include/opencv2/core/hal/interface.h \
  D:/opencv/build/include/opencv2/core/mat.hpp \
  D:/opencv/build/include/opencv2/core/mat.inl.hpp \
  D:/opencv/build/include/opencv2/core/matx.hpp \
  D:/opencv/build/include/opencv2/core/matx.inl.hpp \
  D:/opencv/build/include/opencv2/core/neon_utils.hpp \
  D:/opencv/build/include/opencv2/core/operations.hpp \
  D:/opencv/build/include/opencv2/core/optim.hpp \
  D:/opencv/build/include/opencv2/core/ovx.hpp \
  D:/opencv/build/include/opencv2/core/persistence.hpp \
  D:/opencv/build/include/opencv2/core/saturate.hpp \
  D:/opencv/build/include/opencv2/core/traits.hpp \
  D:/opencv/build/include/opencv2/core/types.hpp \
  D:/opencv/build/include/opencv2/core/utility.hpp \
  D:/opencv/build/include/opencv2/core/utils/logger.defines.hpp \
  D:/opencv/build/include/opencv2/core/utils/logger.hpp \
  D:/opencv/build/include/opencv2/core/utils/logtag.hpp \
  D:/opencv/build/include/opencv2/core/version.hpp \
  D:/opencv/build/include/opencv2/core/vsx_utils.hpp \
  D:/opencv/build/include/opencv2/dnn.hpp \
  D:/opencv/build/include/opencv2/dnn/dict.hpp \
  D:/opencv/build/include/opencv2/dnn/dnn.hpp \
  D:/opencv/build/include/opencv2/dnn/dnn.inl.hpp \
  D:/opencv/build/include/opencv2/dnn/layer.hpp \
  D:/opencv/build/include/opencv2/dnn/utils/inference_engine.hpp \
  D:/opencv/build/include/opencv2/dnn/version.hpp \
  D:/opencv/build/include/opencv2/features2d.hpp \
  D:/opencv/build/include/opencv2/flann.hpp \
  D:/opencv/build/include/opencv2/flann/all_indices.h \
  D:/opencv/build/include/opencv2/flann/allocator.h \
  D:/opencv/build/include/opencv2/flann/any.h \
  D:/opencv/build/include/opencv2/flann/autotuned_index.h \
  D:/opencv/build/include/opencv2/flann/composite_index.h \
  D:/opencv/build/include/opencv2/flann/config.h \
  D:/opencv/build/include/opencv2/flann/defines.h \
  D:/opencv/build/include/opencv2/flann/dist.h \
  D:/opencv/build/include/opencv2/flann/dynamic_bitset.h \
  D:/opencv/build/include/opencv2/flann/flann_base.hpp \
  D:/opencv/build/include/opencv2/flann/general.h \
  D:/opencv/build/include/opencv2/flann/ground_truth.h \
  D:/opencv/build/include/opencv2/flann/heap.h \
  D:/opencv/build/include/opencv2/flann/hierarchical_clustering_index.h \
  D:/opencv/build/include/opencv2/flann/index_testing.h \
  D:/opencv/build/include/opencv2/flann/kdtree_index.h \
  D:/opencv/build/include/opencv2/flann/kdtree_single_index.h \
  D:/opencv/build/include/opencv2/flann/kmeans_index.h \
  D:/opencv/build/include/opencv2/flann/linear_index.h \
  D:/opencv/build/include/opencv2/flann/logger.h \
  D:/opencv/build/include/opencv2/flann/lsh_index.h \
  D:/opencv/build/include/opencv2/flann/lsh_table.h \
  D:/opencv/build/include/opencv2/flann/matrix.h \
  D:/opencv/build/include/opencv2/flann/miniflann.hpp \
  D:/opencv/build/include/opencv2/flann/nn_index.h \
  D:/opencv/build/include/opencv2/flann/params.h \
  D:/opencv/build/include/opencv2/flann/random.h \
  D:/opencv/build/include/opencv2/flann/result_set.h \
  D:/opencv/build/include/opencv2/flann/sampling.h \
  D:/opencv/build/include/opencv2/flann/saving.h \
  D:/opencv/build/include/opencv2/flann/timer.h \
  D:/opencv/build/include/opencv2/highgui.hpp \
  D:/opencv/build/include/opencv2/imgcodecs.hpp \
  D:/opencv/build/include/opencv2/imgproc.hpp \
  D:/opencv/build/include/opencv2/imgproc/segmentation.hpp \
  D:/opencv/build/include/opencv2/ml.hpp \
  D:/opencv/build/include/opencv2/ml/ml.inl.hpp \
  D:/opencv/build/include/opencv2/objdetect.hpp \
  D:/opencv/build/include/opencv2/objdetect/aruco_board.hpp \
  D:/opencv/build/include/opencv2/objdetect/aruco_detector.hpp \
  D:/opencv/build/include/opencv2/objdetect/aruco_dictionary.hpp \
  D:/opencv/build/include/opencv2/objdetect/barcode.hpp \
  D:/opencv/build/include/opencv2/objdetect/charuco_detector.hpp \
  D:/opencv/build/include/opencv2/objdetect/detection_based_tracker.hpp \
  D:/opencv/build/include/opencv2/objdetect/face.hpp \
  D:/opencv/build/include/opencv2/objdetect/graphical_code_detector.hpp \
  D:/opencv/build/include/opencv2/opencv.hpp \
  D:/opencv/build/include/opencv2/opencv_modules.hpp \
  D:/opencv/build/include/opencv2/photo.hpp \
  D:/opencv/build/include/opencv2/stitching.hpp \
  D:/opencv/build/include/opencv2/stitching/detail/blenders.hpp \
  D:/opencv/build/include/opencv2/stitching/detail/camera.hpp \
  D:/opencv/build/include/opencv2/stitching/detail/exposure_compensate.hpp \
  D:/opencv/build/include/opencv2/stitching/detail/matchers.hpp \
  D:/opencv/build/include/opencv2/stitching/detail/motion_estimators.hpp \
  D:/opencv/build/include/opencv2/stitching/detail/seam_finders.hpp \
  D:/opencv/build/include/opencv2/stitching/detail/util.hpp \
  D:/opencv/build/include/opencv2/stitching/detail/util_inl.hpp \
  D:/opencv/build/include/opencv2/stitching/detail/warpers.hpp \
  D:/opencv/build/include/opencv2/stitching/detail/warpers_inl.hpp \
  D:/opencv/build/include/opencv2/stitching/warpers.hpp \
  D:/opencv/build/include/opencv2/video.hpp \
  D:/opencv/build/include/opencv2/video/background_segm.hpp \
  D:/opencv/build/include/opencv2/video/tracking.hpp \
  D:/opencv/build/include/opencv2/videoio.hpp
