
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.11.9+a69bbaaf5
      鐢熸垚鍚姩鏃堕棿涓?2025/6/25 11:35:58銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\4.0.0\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdC.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\QT  pros\\111\\build\\CMakeFiles\\4.0.0\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淒:\\QT  pros\\111\\build\\CMakeFiles\\4.0.0\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:02.36
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/QT  pros/111/build/CMakeFiles/4.0.0/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.11.9+a69bbaaf5
      鐢熸垚鍚姩鏃堕棿涓?2025/6/25 11:36:01銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\4.0.0\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\QT  pros\\111\\build\\CMakeFiles\\4.0.0\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淒:\\QT  pros\\111\\build\\CMakeFiles\\4.0.0\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:02.70
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/QT  pros/111/build/CMakeFiles/4.0.0/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-czprxf"
      binary: "D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-czprxf"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-czprxf'
        
        Run Build Command(s): D:/VS2/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_c878d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.11.9+a69bbaaf5
        鐢熸垚鍚姩鏃堕棿涓?2025/6/25 11:36:04銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-czprxf\\cmTC_c878d.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_c878d.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-czprxf\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_c878d.dir\\Debug\\cmTC_c878d.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_c878d.dir\\Debug\\cmTC_c878d.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_c878d.dir\\Debug\\cmTC_c878d.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_c878d.dir\\Debug\\\\" /Fd"cmTC_c878d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cmake\\data\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.41.34120 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_c878d.dir\\Debug\\\\" /Fd"cmTC_c878d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cmake\\data\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-czprxf\\Debug\\cmTC_c878d.exe" /INCREMENTAL /ILK:"cmTC_c878d.dir\\Debug\\cmTC_c878d.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-czprxf/Debug/cmTC_c878d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-czprxf/Debug/cmTC_c878d.lib" /MACHINE:X64  /machine:x64 cmTC_c878d.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_c878d.vcxproj -> D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-czprxf\\Debug\\cmTC_c878d.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_c878d.dir\\Debug\\cmTC_c878d.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_c878d.dir\\Debug\\cmTC_c878d.tlog\\cmTC_c878d.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-czprxf\\cmTC_c878d.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:02.16
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': D:/VS2/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "D:/VS2/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.41.34120.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-nn4qk6"
      binary: "D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-nn4qk6"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-nn4qk6'
        
        Run Build Command(s): D:/VS2/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_a5815.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.11.9+a69bbaaf5
        鐢熸垚鍚姩鏃堕棿涓?2025/6/25 11:36:07銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nn4qk6\\cmTC_a5815.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_a5815.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nn4qk6\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_a5815.dir\\Debug\\cmTC_a5815.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_a5815.dir\\Debug\\cmTC_a5815.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_a5815.dir\\Debug\\cmTC_a5815.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_a5815.dir\\Debug\\\\" /Fd"cmTC_a5815.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cmake\\data\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.41.34120 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_a5815.dir\\Debug\\\\" /Fd"cmTC_a5815.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cmake\\data\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nn4qk6\\Debug\\cmTC_a5815.exe" /INCREMENTAL /ILK:"cmTC_a5815.dir\\Debug\\cmTC_a5815.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-nn4qk6/Debug/cmTC_a5815.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-nn4qk6/Debug/cmTC_a5815.lib" /MACHINE:X64  /machine:x64 cmTC_a5815.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_a5815.vcxproj -> D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nn4qk6\\Debug\\cmTC_a5815.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_a5815.dir\\Debug\\cmTC_a5815.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_a5815.dir\\Debug\\cmTC_a5815.tlog\\cmTC_a5815.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nn4qk6\\cmTC_a5815.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:02.09
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': D:/VS2/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "D:/VS2/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.41.34120.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-5yqhui"
      binary: "D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-5yqhui"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6;D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-5yqhui'
        
        Run Build Command(s): D:/VS2/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_1e028.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.11.9+a69bbaaf5
        鐢熸垚鍚姩鏃堕棿涓?2025/6/25 11:36:10銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5yqhui\\cmTC_1e028.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_1e028.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5yqhui\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_1e028.dir\\Debug\\cmTC_1e028.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_1e028.dir\\Debug\\cmTC_1e028.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_1e028.dir\\Debug\\cmTC_1e028.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_1e028.dir\\Debug\\\\" /Fd"cmTC_1e028.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5yqhui\\src.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.41.34120 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_1e028.dir\\Debug\\\\" /Fd"cmTC_1e028.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5yqhui\\src.c"
          src.c
        D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5yqhui\\src.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5yqhui\\cmTC_1e028.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5yqhui\\cmTC_1e028.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5yqhui\\cmTC_1e028.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5yqhui\\src.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5yqhui\\cmTC_1e028.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.02
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-bc9rpv"
      binary: "D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-bc9rpv"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6;D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-bc9rpv'
        
        Run Build Command(s): D:/VS2/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_8d472.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.11.9+a69bbaaf5
        鐢熸垚鍚姩鏃堕棿涓?2025/6/25 11:36:12銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bc9rpv\\cmTC_8d472.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_8d472.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bc9rpv\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_8d472.dir\\Debug\\cmTC_8d472.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_8d472.dir\\Debug\\cmTC_8d472.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_8d472.dir\\Debug\\cmTC_8d472.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_8d472.dir\\Debug\\\\" /Fd"cmTC_8d472.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bc9rpv\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.41.34120 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_8d472.dir\\Debug\\\\" /Fd"cmTC_8d472.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bc9rpv\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bc9rpv\\Debug\\cmTC_8d472.exe" /INCREMENTAL /ILK:"cmTC_8d472.dir\\Debug\\cmTC_8d472.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-bc9rpv/Debug/cmTC_8d472.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-bc9rpv/Debug/cmTC_8d472.lib" /MACHINE:X64  /machine:x64 cmTC_8d472.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bc9rpv\\cmTC_8d472.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bc9rpv\\cmTC_8d472.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bc9rpv\\cmTC_8d472.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bc9rpv\\cmTC_8d472.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.68
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-csn1ue"
      binary: "D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-csn1ue"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6;D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-csn1ue'
        
        Run Build Command(s): D:/VS2/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_fd063.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.11.9+a69bbaaf5
        鐢熸垚鍚姩鏃堕棿涓?2025/6/25 11:36:14銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-csn1ue\\cmTC_fd063.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_fd063.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-csn1ue\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_fd063.dir\\Debug\\cmTC_fd063.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_fd063.dir\\Debug\\cmTC_fd063.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_fd063.dir\\Debug\\cmTC_fd063.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_fd063.dir\\Debug\\\\" /Fd"cmTC_fd063.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-csn1ue\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.41.34120 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_fd063.dir\\Debug\\\\" /Fd"cmTC_fd063.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-csn1ue\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-csn1ue\\Debug\\cmTC_fd063.exe" /INCREMENTAL /ILK:"cmTC_fd063.dir\\Debug\\cmTC_fd063.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-csn1ue/Debug/cmTC_fd063.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-csn1ue/Debug/cmTC_fd063.lib" /MACHINE:X64  /machine:x64 cmTC_fd063.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-csn1ue\\cmTC_fd063.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-csn1ue\\cmTC_fd063.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-csn1ue\\cmTC_fd063.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-csn1ue\\cmTC_fd063.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.70
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:45 (include)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:218 (find_package)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-ib7j63"
      binary: "D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-ib7j63"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6;D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: 'D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-ib7j63'
        
        Run Build Command(s): D:/VS2/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_825f4.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.11.9+a69bbaaf5
        鐢熸垚鍚姩鏃堕棿涓?2025/6/25 11:36:16銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ib7j63\\cmTC_825f4.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_825f4.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ib7j63\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_825f4.dir\\Debug\\cmTC_825f4.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_825f4.dir\\Debug\\cmTC_825f4.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_825f4.dir\\Debug\\cmTC_825f4.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_825f4.dir\\Debug\\\\" /Fd"cmTC_825f4.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ib7j63\\src.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.41.34120 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_825f4.dir\\Debug\\\\" /Fd"cmTC_825f4.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ib7j63\\src.cxx"
          src.cxx
        Link:
          D:\\VS2\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ib7j63\\Debug\\cmTC_825f4.exe" /INCREMENTAL /ILK:"cmTC_825f4.dir\\Debug\\cmTC_825f4.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-ib7j63/Debug/cmTC_825f4.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/QT  pros/111/build/CMakeFiles/CMakeScratch/TryCompile-ib7j63/Debug/cmTC_825f4.lib" /MACHINE:X64  /machine:x64 cmTC_825f4.dir\\Debug\\src.obj
          cmTC_825f4.vcxproj -> D:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ib7j63\\Debug\\cmTC_825f4.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_825f4.dir\\Debug\\cmTC_825f4.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_825f4.dir\\Debug\\cmTC_825f4.tlog\\cmTC_825f4.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\QT  pros\\111\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ib7j63\\cmTC_825f4.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:02.22
        
      exitCode: 0
...
