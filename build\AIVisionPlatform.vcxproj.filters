﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\QT  pros\111\build\AIVisionPlatform_autogen\mocs_compilation_Debug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QT  pros\111\src\core\GeminiAPIManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QT  pros\111\src\core\OpenCVVisionEngine.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QT  pros\111\src\core\VideoFrameCapture.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QT  pros\111\src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QT  pros\111\src\ui\DetectionOverlay.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QT  pros\111\src\ui\MainWindow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QT  pros\111\build\AIVisionPlatform_autogen\mocs_compilation_Release.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QT  pros\111\build\AIVisionPlatform_autogen\mocs_compilation_MinSizeRel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QT  pros\111\build\AIVisionPlatform_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\QT  pros\111\src\core\GeminiAPIManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\QT  pros\111\include\DetectionOverlay.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\QT  pros\111\include\MainWindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\QT  pros\111\include\OpenCVVisionEngine.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\QT  pros\111\include\VideoFrameCapture.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\QT  pros\111\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{28288681-D144-3E83-923D-378ADD8809AD}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{E8D16EB7-7B73-30BA-8794-2E7AFC01E16D}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
