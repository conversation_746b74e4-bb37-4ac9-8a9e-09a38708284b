# AI图像识别平台

一个基于Qt6的实时AI图像识别平台，支持摄像头捕获、实时推理和结果显示。

## ✨ 功能特性

### 🎯 已实现功能
- ✅ **实时摄像头捕获** - 基于Qt6原生多媒体框架
- ✅ **直观用户界面** - 现代化的深色主题界面
- ✅ **性能监控** - 实时FPS、内存、CPU使用率显示
- ✅ **摄像头管理** - 自动检测和选择可用摄像头
- ✅ **状态监控** - 实时状态更新和错误处理

### 🚀 计划功能
- 🔄 **AI模型集成** - TensorFlow Lite推理引擎
- 🔄 **实时识别** - 物体检测和分类
- 🔄 **结果显示** - 边界框和标签叠加
- 🔄 **图像保存** - 捕获和保存识别结果
- 🔄 **视频录制** - 录制带识别结果的视频
- 🔄 **模型管理** - 动态加载不同AI模型
- 🔄 **设置界面** - 可配置的参数和选项

## 🛠️ 技术栈

- **框架**: Qt6 (Core, Widgets, Multimedia, MultimediaWidgets)
- **编译器**: MinGW 13.1.0
- **构建系统**: CMake 3.16+
- **AI引擎**: TensorFlow Lite (计划中)
- **视频处理**: Qt6 Multimedia (FFmpeg后端)

## 📋 系统要求

- Windows 10/11
- Qt6.9.1 或更高版本
- MinGW 13.1.0 编译器
- 摄像头设备
- 至少4GB RAM

## 🚀 快速开始

### 编译项目

```bash
# 创建构建目录
mkdir build
cd build

# 配置项目
cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release -DCMAKE_PREFIX_PATH="D:\QT\6.9.1\mingw_64" ..

# 编译
cmake --build . --config Release
```

### 运行程序

```bash
# 设置环境变量
$env:PATH="D:\QT\6.9.1\mingw_64\bin;D:\QT\Tools\mingw1310_64\bin;$env:PATH"

# 运行
.\bin\AIVisionPlatform.exe
```

## 📖 使用说明

1. **启动程序** - 运行AIVisionPlatform.exe
2. **打开摄像头** - 点击左侧"打开摄像头"按钮
3. **选择设备** - 从列表中选择摄像头设备
4. **查看视频** - 中央区域显示实时视频流
5. **监控性能** - 右侧面板显示实时性能数据

## 🏗️ 项目结构

```
AIVisionPlatform/
├── main.cpp              # 主程序入口
├── CMakeLists.txt         # CMake构建配置
├── README.md              # 项目说明
├── build/                 # 构建输出目录
└── bin/                   # 可执行文件目录
```

## 🔧 开发计划

### 阶段1：完善基础功能 ⭐
- [x] 摄像头功能
- [ ] 图像保存功能
- [ ] 录制功能
- [ ] 改进性能监控

### 阶段2：集成AI功能 ⭐⭐
- [ ] 集成TensorFlow Lite
- [ ] 添加模型加载
- [ ] 实现实时推理
- [ ] 显示识别结果

### 阶段3：用户体验优化 ⭐⭐⭐
- [ ] 添加设置界面
- [ ] 结果历史记录
- [ ] 导出功能
- [ ] 主题和样式

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目采用MIT许可证 - 查看LICENSE文件了解详情。

## 🙏 致谢

- Qt框架提供的强大多媒体支持
- FFmpeg提供的视频处理能力
- TensorFlow Lite提供的AI推理引擎
