#include "GeminiAPIManager.h"
#include <QBuffer>
#include <QHttpMultiPart>
#include <QHttpPart>
#include <QUrlQuery>
#include <QUrl>
#include <QDebug>
#include <QStandardPaths>
#include <QDir>

// API配置常量
const QString GeminiAPIManager::API_BASE_URL = "https://generativelanguage.googleapis.com";
const QString GeminiAPIManager::API_ENDPOINT = "/v1beta/models/gemini-2.0-flash-exp:generateContent";
const QString GeminiAPIManager::DEFAULT_MODEL = "gemini-2.0-flash-exp";

GeminiAPIManager::GeminiAPIManager(QObject *parent)
    : QObject(parent)
    , networkManager_(new QNetworkAccessManager(this))
    , settings_(new QSettings("AIVisionPlatform", "GeminiAPI", this))
{
    qDebug() << "GeminiAPIManager initialized";
    loadSettings();
}

GeminiAPIManager::~GeminiAPIManager()
{
    saveSettings();
}

void GeminiAPIManager::setApiKey(const QString& apiKey)
{
    apiKey_ = apiKey;
    saveSettings();
    qDebug() << "Gemini API key updated";
}

QString GeminiAPIManager::getApiKey() const
{
    return apiKey_;
}

bool GeminiAPIManager::hasValidApiKey() const
{
    return !apiKey_.isEmpty() && apiKey_.length() > 20; // 基本验证
}

void GeminiAPIManager::analyzeImage(const QImage& image, const QString& prompt)
{
    if (!hasValidApiKey()) {
        emit analysisError("API密钥未设置或无效。请先设置有效的Gemini API密钥。");
        return;
    }

    if (image.isNull()) {
        emit analysisError("图像无效，无法进行分析。");
        return;
    }

    emit analysisProgress("正在准备图像数据...");

    // 转换图像为Base64
    QString base64Image = imageToBase64(image);
    if (base64Image.isEmpty()) {
        emit analysisError("图像转换失败。");
        return;
    }

    // 使用默认或自定义提示词
    QString analysisPrompt = prompt.isEmpty() ? getDefaultPrompt() : prompt;
    
    emit analysisProgress("正在发送请求到Gemini API...");

    // 构建请求
    QJsonObject requestJson = createAnalysisRequest(base64Image, analysisPrompt);
    QJsonDocument requestDoc(requestJson);

    // 构建URL
    QUrl url(API_BASE_URL + API_ENDPOINT);
    QUrlQuery query;
    query.addQueryItem("key", apiKey_);
    url.setQuery(query);

    // 调试信息
    qDebug() << "=== Gemini API Request Debug ===";
    qDebug() << "API Key length:" << apiKey_.length();
    qDebug() << "API Key (first 10 chars):" << apiKey_.left(10);
    qDebug() << "API Key (last 10 chars):" << apiKey_.right(10);
    qDebug() << "Full URL:" << url.toString();

    // 创建请求
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    request.setRawHeader("User-Agent", "AIVisionPlatform/1.0");

    // 发送请求
    QNetworkReply* reply = networkManager_->post(request, requestDoc.toJson());

    // 连接信号
    connect(reply, &QNetworkReply::finished, this, &GeminiAPIManager::handleNetworkReply);
    connect(reply, QOverload<QNetworkReply::NetworkError>::of(&QNetworkReply::errorOccurred),
            this, &GeminiAPIManager::handleNetworkError);

    qDebug() << "Gemini API request sent successfully";
}

void GeminiAPIManager::analyzeImageWithCustomPrompt(const QImage& image, const QString& customPrompt)
{
    analyzeImage(image, customPrompt);
}

QString GeminiAPIManager::imageToBase64(const QImage& image)
{
    if (image.isNull()) {
        return QString();
    }

    // 调整图像大小以减少API调用成本
    QImage resizedImage = image;
    if (image.width() > 1024 || image.height() > 1024) {
        resizedImage = image.scaled(1024, 1024, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    }

    // 转换为PNG格式的Base64
    QByteArray byteArray;
    QBuffer buffer(&byteArray);
    buffer.open(QIODevice::WriteOnly);
    
    if (!resizedImage.save(&buffer, "PNG")) {
        qDebug() << "Failed to save image to buffer";
        return QString();
    }

    return QString::fromLatin1(byteArray.toBase64());
}

QJsonObject GeminiAPIManager::createAnalysisRequest(const QString& base64Image, const QString& prompt)
{
    QJsonObject request;
    
    // 构建contents数组
    QJsonArray contents;
    QJsonObject content;
    QJsonArray parts;
    
    // 添加图像部分
    QJsonObject imagePart;
    QJsonObject inlineData;
    inlineData["mimeType"] = "image/png";
    inlineData["data"] = base64Image;
    imagePart["inlineData"] = inlineData;
    parts.append(imagePart);
    
    // 添加文本提示部分
    QJsonObject textPart;
    textPart["text"] = prompt;
    parts.append(textPart);
    
    content["parts"] = parts;
    contents.append(content);
    
    request["contents"] = contents;
    
    // 添加生成配置
    QJsonObject generationConfig;
    generationConfig["temperature"] = 0.4;
    generationConfig["topK"] = 32;
    generationConfig["topP"] = 1.0;
    generationConfig["maxOutputTokens"] = 4096;
    request["generationConfig"] = generationConfig;
    
    return request;
}

QString GeminiAPIManager::getDefaultPrompt() const
{
    return "请详细分析这张图片，包括：\n"
           "1. 图片中的主要物体和人物\n"
           "2. 场景描述（室内/室外、环境等）\n"
           "3. 主要颜色\n"
           "4. 情感或氛围\n"
           "5. 任何值得注意的细节\n\n"
           "请用中文回答，并尽可能详细和准确。";
}

QString GeminiAPIManager::getDetailedAnalysisPrompt() const
{
    return "作为一个专业的图像分析师，请对这张图片进行全面分析：\n\n"
           "**物体识别：**\n"
           "- 列出图片中所有可识别的物体、动物、人物\n"
           "- 描述它们的位置、大小、状态\n\n"
           "**场景分析：**\n"
           "- 确定场景类型（室内/室外、具体环境）\n"
           "- 描述背景和环境特征\n\n"
           "**视觉元素：**\n"
           "- 主要颜色和色调\n"
           "- 光线条件和阴影\n"
           "- 构图和视角\n\n"
           "**情感和氛围：**\n"
           "- 图片传达的情感或氛围\n"
           "- 人物表情（如有）\n\n"
           "**技术细节：**\n"
           "- 图片质量和清晰度\n"
           "- 拍摄角度和距离\n\n"
           "请用中文详细回答，条理清晰。";
}

void GeminiAPIManager::loadSettings()
{
    apiKey_ = settings_->value("apiKey", "").toString();
    qDebug() << "Loaded Gemini API settings";
}

void GeminiAPIManager::saveSettings()
{
    settings_->setValue("apiKey", apiKey_);
    settings_->sync();
    qDebug() << "Saved Gemini API settings";
}

void GeminiAPIManager::handleNetworkReply()
{
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) {
        return;
    }

    reply->deleteLater();

    emit analysisProgress("正在处理Gemini响应...");

    if (reply->error() != QNetworkReply::NoError) {
        QString errorMsg = QString("网络错误: %1").arg(reply->errorString());
        qDebug() << "Network error:" << errorMsg;
        emit analysisError(errorMsg);
        return;
    }

    // 读取响应数据
    QByteArray responseData = reply->readAll();
    qDebug() << "Received response size:" << responseData.size() << "bytes";

    // 解析JSON响应
    QJsonParseError parseError;
    QJsonDocument responseDoc = QJsonDocument::fromJson(responseData, &parseError);

    if (parseError.error != QJsonParseError::NoError) {
        QString errorMsg = QString("JSON解析错误: %1").arg(parseError.errorString());
        qDebug() << "JSON parse error:" << errorMsg;
        qDebug() << "Response data:" << responseData;
        emit analysisError(errorMsg);
        return;
    }

    QJsonObject responseObj = responseDoc.object();

    // 检查API错误
    if (responseObj.contains("error")) {
        handleApiError(responseObj);
        return;
    }

    // 解析分析结果
    GeminiAnalysisResult result = parseResponse(responseObj);

    if (result.success) {
        emit analysisProgress("分析完成！");
        emit analysisCompleted(result);
    } else {
        emit analysisError(result.errorMessage);
    }
}

void GeminiAPIManager::handleNetworkError(QNetworkReply::NetworkError error)
{
    QString errorMsg = QString("网络连接错误 (代码: %1)").arg(static_cast<int>(error));
    qDebug() << "Network error occurred:" << errorMsg;
    emit analysisError(errorMsg);
}

GeminiAnalysisResult GeminiAPIManager::parseResponse(const QJsonObject& response)
{
    GeminiAnalysisResult result;

    try {
        // 提取主要文本内容
        QString analysisText = extractTextFromResponse(response);

        if (analysisText.isEmpty()) {
            result.errorMessage = "无法从响应中提取分析结果";
            return result;
        }

        // 设置基本结果
        result.description = analysisText;
        result.detailedAnalysis = analysisText;
        result.success = true;

        // 简单的关键词提取（可以后续优化）
        QString lowerText = analysisText.toLower();

        // 提取可能的物体
        QStringList commonObjects = {"人", "车", "狗", "猫", "熊", "鸟", "树", "花", "建筑", "房子", "桌子", "椅子"};
        for (const QString& obj : commonObjects) {
            if (lowerText.contains(obj)) {
                result.detectedObjects.append(obj);
            }
        }

        // 提取颜色
        QStringList colors = {"红", "绿", "蓝", "黄", "黑", "白", "灰", "棕", "粉", "紫"};
        for (const QString& color : colors) {
            if (lowerText.contains(color)) {
                result.colors.append(color);
            }
        }

        qDebug() << "Successfully parsed Gemini response";
        qDebug() << "Analysis length:" << analysisText.length() << "characters";

    } catch (const std::exception& e) {
        result.success = false;
        result.errorMessage = QString("解析响应时发生错误: %1").arg(e.what());
        qDebug() << "Exception in parseResponse:" << e.what();
    }

    return result;
}

QString GeminiAPIManager::extractTextFromResponse(const QJsonObject& response)
{
    // 标准Gemini API响应格式: candidates[0].content.parts[0].text
    if (!response.contains("candidates")) {
        qDebug() << "No 'candidates' field in response";
        return QString();
    }

    QJsonArray candidates = response["candidates"].toArray();
    if (candidates.isEmpty()) {
        qDebug() << "Empty candidates array";
        return QString();
    }

    QJsonObject firstCandidate = candidates[0].toObject();
    if (!firstCandidate.contains("content")) {
        qDebug() << "No 'content' field in first candidate";
        return QString();
    }

    QJsonObject content = firstCandidate["content"].toObject();
    if (!content.contains("parts")) {
        qDebug() << "No 'parts' field in content";
        return QString();
    }

    QJsonArray parts = content["parts"].toArray();
    if (parts.isEmpty()) {
        qDebug() << "Empty parts array";
        return QString();
    }

    QJsonObject firstPart = parts[0].toObject();
    if (!firstPart.contains("text")) {
        qDebug() << "No 'text' field in first part";
        return QString();
    }

    return firstPart["text"].toString();
}

void GeminiAPIManager::handleApiError(const QJsonObject& response)
{
    QJsonObject error = response["error"].toObject();
    QString errorMessage = error["message"].toString();
    int errorCode = error["code"].toInt();

    QString fullErrorMsg = QString("Gemini API错误 (代码: %1): %2").arg(errorCode).arg(errorMessage);
    qDebug() << "API error:" << fullErrorMsg;

    emit analysisError(fullErrorMsg);
}
