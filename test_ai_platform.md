# AI图像识别平台测试指南

## 🎯 测试步骤

### 第一步：启动程序
✅ **已完成** - AI图像识别平台已启动
- 程序显示：TensorFlow Lite Engine initialized
- Qt多媒体系统正常工作

### 第二步：测试摄像头功能
请按以下步骤操作：

1. **打开摄像头**
   - 点击左侧面板的"打开摄像头"按钮
   - 从弹出的对话框中选择"Integrated Camera"
   - 确认摄像头画面显示在中央区域

2. **验证摄像头状态**
   - 按钮文字应变为"关闭摄像头"
   - 状态栏显示"摄像头已启动"
   - 右侧性能监控显示FPS: 30.0

### 第三步：测试AI模型加载
1. **加载测试模型**
   - 点击菜单栏"文件" → "加载AI模型"
   - 浏览到项目的`models`目录
   - 选择`test_model.tflite`文件
   - 点击"打开"

2. **验证模型加载**
   - 应该显示"AI模型加载成功"对话框
   - 对话框中显示模型信息
   - 状态栏显示"AI模型已加载"

### 第四步：测试AI识别功能
1. **开始AI识别**
   - 确保摄像头已打开
   - 点击左侧面板的"开始识别"按钮
   - 按钮应变为红色的"停止识别"

2. **观察AI识别效果**
   - 视频画面上应出现彩色边界框
   - 边界框上显示物体名称和置信度
   - 状态栏显示检测到的物体信息
   - 右侧性能监控显示AI统计信息

3. **验证检测结果**
   - 应该看到类似："检测到: person(85.2%), car(72.1%)"
   - 边界框颜色：不同类别使用不同颜色
   - 实时更新：检测结果每100ms更新一次

### 第五步：测试图像捕获功能
1. **捕获图像**
   - 点击橙色的"捕获图像"按钮
   - 状态栏显示"图像已保存: capture_YYYYMMDD_HHMMSS.jpg"

2. **另存为功能**
   - 点击紫色的"另存为..."按钮
   - 选择保存位置和文件名
   - 确认图像保存成功

### 第六步：测试视频录制功能
1. **开始录制**
   - 点击绿色的"开始录制"按钮
   - 按钮变为红色的"停止录制"
   - 状态栏显示录制文件名

2. **观察录制状态**
   - 状态栏显示录制时间：如"录制时间: 00:05"
   - 录制过程中AI识别结果也会被录制

3. **停止录制**
   - 点击红色的"停止录制"按钮
   - 按钮变回绿色的"开始录制"
   - 状态栏显示"录制已停止"

## 📊 预期结果

### 性能监控显示
```
FPS: 30.0
延迟: 33ms
内存: 512MB
CPU: 25.0%
GPU: 15.0%
---AI统计---
推理FPS: 10.0
平均耗时: 100.0ms
处理帧数: 50
```

### 文件输出
- **图像文件**: `图片/AIVision/capture_*.jpg`
- **视频文件**: `视频/AIVision/recording_*.mp4`

## 🔍 故障排除

### 如果摄像头无法打开
- 检查摄像头是否被其他程序占用
- 重启程序再试

### 如果AI识别无反应
- 确保已加载模型文件
- 确保摄像头已打开
- 检查"开始识别"按钮是否变为红色

### 如果没有检测结果
- 这是正常的，因为使用的是演示模式
- 真实模型会产生实际的检测结果

## ✅ 测试完成标准

所有功能正常工作时，您应该看到：
1. ✅ 摄像头实时画面
2. ✅ AI检测边界框和标签
3. ✅ 实时性能统计
4. ✅ 图像保存功能
5. ✅ 视频录制功能
6. ✅ 模型加载功能

请按照上述步骤进行测试，并告诉我每一步的结果！
