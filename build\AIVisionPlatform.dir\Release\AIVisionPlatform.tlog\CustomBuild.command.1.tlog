^D:\QT  PROS\111\CMAKELISTS.TXT
setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\bin\cmake.exe "-SD:/QT  pros/111" "-BD:/QT  pros/111/build" --check-stamp-file "D:/QT  pros/111/build/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
